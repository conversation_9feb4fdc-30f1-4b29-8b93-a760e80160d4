// pages/data-center/index.js
const SmartApi = require('../../utils/smartApi')

Page({
  data: {
    // 考试准备度
    preparationScore: 78,
    preparationLevel: '良好',
    preparationDescription: '继续保持，距离目标还有一步',
    scoreFactors: [],
    showScoreInfo: false,
    scoreFactorExplanations: [],

    // 复习统计
    currentTimeRange: 'week',
    timeRangeOptions: [],
    studyStats: [],

    // 效率分析
    efficiencyData: [],
    efficiencyInsights: [],

    // 科目分析
    subjectsData: [],

    // 备考习惯
    studyHabits: [],

    // 成就
    recentAchievements: []
  },

  onLoad() {
    this.initPage()
  },

  async onShow() {
    await this.loadData()
  },

  // 初始化页面
  async initPage() {
    this.initTimeRangeOptions()
    this.initScoreFactorExplanations()
    await this.loadData()
  },

  // 加载数据
  async loadData() {
    wx.showLoading({
      title: '加载中...',
      mask: true
    })

    try {
      await Promise.all([
        this.loadStudyStats(),
        this.loadEfficiencyData(),
        this.loadSubjectsData(),
        this.loadStudyHabits(),
        this.loadRecentAchievements()
      ])

      wx.hideLoading()
    } catch (error) {
      wx.hideLoading()
      console.error('加载数据失败:', error)
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      })
    }
  },

  // 初始化时间范围选项
  initTimeRangeOptions() {
    const timeRangeOptions = [
      { value: 'week', label: '本周' },
      { value: 'month', label: '本月' },
      { value: 'quarter', label: '本季度' },
      { value: 'year', label: '本年' }
    ]

    this.setData({ timeRangeOptions })
  },

  // 初始化评分因子说明
  initScoreFactorExplanations() {
    const scoreFactorExplanations = [
      {
        name: '复习时长',
        description: '每日复习时间的稳定性和充足性'
      },
      {
        name: '计划完成',
        description: '复习计划的完成率和质量'
      },
      {
        name: '知识掌握',
        description: '通过测试和练习反映的知识掌握程度'
      },
      {
        name: '复习效率',
        description: '单位时间内的复习效果和专注度'
      }
    ]

    this.setData({ scoreFactorExplanations })
  },

  // 加载数据
  loadData() {
    this.loadScoreFactors()
    this.loadStudyStats()
    this.loadEfficiencyData()
    this.loadSubjectsData()
    this.loadStudyHabits()
    this.loadRecentAchievements()
  },

  // 加载评分因子
  loadScoreFactors() {
    const scoreFactors = [
      {
        name: '复习时长',
        score: 85,
        color: '#52C41A'
      },
      {
        name: '计划完成',
        score: 72,
        color: '#1890FF'
      },
      {
        name: '知识掌握',
        score: 68,
        color: '#FA8C16'
      },
      {
        name: '复习效率',
        score: 88,
        color: '#722ED1'
      }
    ]

    this.setData({ scoreFactors })
  },

  // 加载复习统计
  async loadStudyStats() {
    try {
      const timeRange = this.data.currentTimeRange
      const result = await SmartApi.getUserStats(timeRange)

      if (result.success && result.data) {
        const data = result.data

        const studyStats = [
          {
            icon: '⏰',
            label: '复习时长',
            value: data.studyTime || '0h',
            bgColor: '#E6F7FF',
            trend: {
              icon: '📈',
              text: '+12%' // 可以根据历史数据计算趋势
            }
          },
          {
            icon: '📝',
            label: '完成复习',
            value: `${data.completedTasks || 0}个`,
            bgColor: '#F6FFED',
            trend: {
              icon: '📈',
              text: '+8%'
            }
          },
          {
            icon: '🍅',
            label: '专注次数',
            value: `${data.pomodoroCount || 0}次`,
            bgColor: '#FFF7E6',
            trend: {
              icon: '📈',
              text: '+15%'
            }
          },
          {
            icon: '📊',
            label: '平均效率',
            value: `${data.averageEfficiency || 0}%`,
            bgColor: '#F9F0FF',
            trend: {
              icon: '📈',
              text: '+5%'
            }
          }
        ]

        this.setData({ studyStats })
      } else {
        // 使用默认数据
        this.setDefaultStudyStats()
      }
    } catch (error) {
      console.error('加载复习统计失败:', error)
      this.setDefaultStudyStats()
    }
  },

  // 设置默认统计数据
  setDefaultStudyStats() {
    const studyStats = [
      {
        icon: '⏰',
        label: '复习时长',
        value: '0h',
        bgColor: '#E6F7FF',
        trend: { icon: '📈', text: '0%' }
      },
      {
        icon: '📝',
        label: '完成复习',
        value: '0个',
        bgColor: '#F6FFED',
        trend: { icon: '📈', text: '0%' }
      },
      {
        icon: '🍅',
        label: '专注次数',
        value: '0次',
        bgColor: '#FFF7E6',
        trend: { icon: '📈', text: '0%' }
      },
      {
        icon: '📊',
        label: '平均效率',
        value: '0%',
        bgColor: '#F9F0FF',
        trend: { icon: '📈', text: '0%' }
      }
    ]

    this.setData({ studyStats })
  },

  // 加载效率数据
  async loadEfficiencyData() {
    try {
      // 获取最近7天的统计数据
      const endDate = new Date().toISOString().split('T')[0]
      const startDate = new Date()
      startDate.setDate(startDate.getDate() - 6)
      const startDateStr = startDate.toISOString().split('T')[0]

      const result = await wx.cloud.callFunction({
        name: 'statsManager',
        data: {
          action: 'getStatsRange',
          data: { startDate: startDateStr, endDate: endDate }
        }
      })

      if (result.result.success && result.result.data) {
        const dailyData = result.result.data
        const efficiencyData = this.processEfficiencyData(dailyData)
        const efficiencyInsights = this.generateEfficiencyInsights(dailyData)

        this.setData({ efficiencyData, efficiencyInsights })
      } else {
        this.setDefaultEfficiencyData()
      }
    } catch (error) {
      console.error('加载效率数据失败:', error)
      this.setDefaultEfficiencyData()
    }
  },

  // 处理效率数据
  processEfficiencyData(dailyData) {
    const days = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
    const efficiencyData = []

    for (let i = 6; i >= 0; i--) {
      const date = new Date()
      date.setDate(date.getDate() - i)
      const dateStr = date.toISOString().split('T')[0]
      const dayData = dailyData.find(d => d.date === dateStr)

      efficiencyData.push({
        date: dateStr,
        day: days[date.getDay()],
        efficiency: dayData ? dayData.efficiency || 0 : 0
      })
    }

    return efficiencyData
  },

  // 生成效率洞察
  generateEfficiencyInsights(dailyData) {
    const insights = [
      {
        id: 'peak_time',
        icon: '🌟',
        text: '你的复习效率在上午9-11点最高'
      },
      {
        id: 'consistency',
        icon: '📈',
        text: '本周复习效率保持稳定'
      },
      {
        id: 'suggestion',
        icon: '💡',
        text: '建议在效率高峰期安排重要复习'
      }
    ]

    return insights
  },

  // 设置默认效率数据
  setDefaultEfficiencyData() {
    const efficiencyData = []
    const days = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']

    for (let i = 6; i >= 0; i--) {
      const date = new Date()
      date.setDate(date.getDate() - i)
      efficiencyData.push({
        date: date.toISOString().split('T')[0],
        day: days[date.getDay()],
        efficiency: 0
      })
    }

    const efficiencyInsights = [
      {
        id: 'start',
        icon: '🌱',
        text: '开始记录你的复习效率吧'
      }
    ]

    this.setData({ efficiencyData, efficiencyInsights })
  },

  // 加载科目数据
  async loadSubjectsData() {
    try {
      // 获取用户的考试和任务数据来分析科目
      const [examsResult, tasksResult] = await Promise.all([
        SmartApi.getExams({}, 50, 0),
        SmartApi.getTasks({}, 100, 0)
      ])

      const subjectsData = this.processSubjectsData(examsResult, tasksResult)
      this.setData({ subjectsData })
    } catch (error) {
      console.error('加载科目数据失败:', error)
      this.setDefaultSubjectsData()
    }
  },

  // 处理科目数据
  processSubjectsData(examsResult, tasksResult) {
    const subjectsMap = {}

    // 从任务中提取科目信息
    if (tasksResult.success && tasksResult.data) {
      tasksResult.data.forEach(task => {
        const subject = task.subject || '其他'
        if (!subjectsMap[subject]) {
          subjectsMap[subject] = {
            name: subject,
            exam: task.examName || '未指定考试',
            score: 0,
            progress: 0,
            progressColor: '#52C41A',
            studyTime: 0,
            completedTasks: 0,
            totalTasks: 0,
            avgEfficiency: 0
          }
        }

        subjectsMap[subject].totalTasks++
        if (task.completed) {
          subjectsMap[subject].completedTasks++
        }
      })
    }

    // 计算进度和效率
    Object.keys(subjectsMap).forEach(subject => {
      const data = subjectsMap[subject]
      data.progress = data.totalTasks > 0 ? Math.round((data.completedTasks / data.totalTasks) * 100) : 0
      data.progressColor = data.progress >= 80 ? '#52C41A' : data.progress >= 60 ? '#FA8C16' : '#FF4D4F'
      data.avgEfficiency = `${Math.min(100, Math.max(0, data.progress + Math.random() * 20 - 10))}%`
      data.studyTime = `${Math.round(data.completedTasks * 2.5)}h`
      data.completedTasks = `${data.completedTasks}个`
      data.score = Math.round(data.progress * 0.8 + Math.random() * 20)
    })

    const result = Object.values(subjectsMap).slice(0, 5)
    return result.length > 0 ? result : this.getDefaultSubjectsData()
  },

  // 获取默认科目数据
  getDefaultSubjectsData() {
    return [
      {
        name: '开始学习',
        exam: '制定你的复习计划',
        score: 0,
        progress: 0,
        progressColor: '#CCCCCC',
        studyTime: '0h',
        completedTasks: '0个',
        avgEfficiency: '0%'
      }
    ]
  },

  // 设置默认科目数据
  setDefaultSubjectsData() {
    this.setData({ subjectsData: this.getDefaultSubjectsData() })
  },

  // 加载备考习惯
  async loadStudyHabits() {
    try {
      const statsResult = await SmartApi.getUserStats('month')
      const pomodoroResult = await SmartApi.getPomodoroStats({
        dateRange: {
          start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
          end: new Date().toISOString()
        }
      })

      const studyHabits = this.processStudyHabits(statsResult, pomodoroResult)
      this.setData({ studyHabits })
    } catch (error) {
      console.error('加载备考习惯失败:', error)
      this.setDefaultStudyHabits()
    }
  },

  // 处理学习习惯数据
  processStudyHabits(statsResult, pomodoroResult) {
    const studyHabits = []

    // 最佳复习时段（简化处理）
    studyHabits.push({
      title: '最佳复习时段',
      icon: '🌅',
      value: '9:00-11:00',
      description: '效率最高的时间段'
    })

    // 平均专注时长
    let avgFocusTime = '25分钟'
    if (pomodoroResult.success && pomodoroResult.data && pomodoroResult.data.length > 0) {
      const totalDuration = pomodoroResult.data.reduce((sum, session) => sum + (session.duration || 1500), 0)
      const avgSeconds = totalDuration / pomodoroResult.data.length
      avgFocusTime = `${Math.round(avgSeconds / 60)}分钟`
    }

    studyHabits.push({
      title: '平均专注时长',
      icon: '⏱️',
      value: avgFocusTime,
      description: '单次专注的平均时长'
    })

    // 备考连续天数
    let streakDays = '0天'
    if (statsResult.success && statsResult.data) {
      streakDays = `${statsResult.data.studyDays || 0}天`
    }

    studyHabits.push({
      title: '备考连续天数',
      icon: '🔥',
      value: streakDays,
      description: '当前连续备考记录'
    })

    // 最爱复习地点（固定值，可以后续扩展）
    studyHabits.push({
      title: '最爱复习地点',
      icon: '📍',
      value: '图书馆',
      description: '效率最高的复习环境'
    })

    return studyHabits
  },

  // 设置默认学习习惯
  setDefaultStudyHabits() {
    const studyHabits = [
      {
        title: '最佳复习时段',
        icon: '🌅',
        value: '待分析',
        description: '开始复习后将为你分析'
      },
      {
        title: '平均专注时长',
        icon: '⏱️',
        value: '0分钟',
        description: '使用番茄钟开始专注复习'
      },
      {
        title: '备考连续天数',
        icon: '🔥',
        value: '0天',
        description: '开始你的备考之旅'
      },
      {
        title: '最爱复习地点',
        icon: '📍',
        value: '待发现',
        description: '找到最适合你的复习环境'
      }
    ]

    this.setData({ studyHabits })
  },

  // 加载最近成就
  loadRecentAchievements() {
    const recentAchievements = [
      {
        id: 'streak_15',
        icon: '🔥',
        name: '备考达人',
        description: '连续备考15天',
        time: '2小时前',
        isNew: true
      },
      {
        id: 'efficiency_90',
        icon: '⚡',
        name: '效率之星',
        description: '单日效率达到90%',
        time: '昨天',
        isNew: false
      },
      {
        id: 'pomodoro_50',
        icon: '🍅',
        name: '专注大师',
        description: '完成50次番茄钟',
        time: '3天前',
        isNew: false
      }
    ]

    this.setData({ recentAchievements })
  },

  // 切换时间范围
  switchTimeRange(e) {
    const range = e.currentTarget.dataset.range
    this.setData({ currentTimeRange: range })
    this.loadStudyStats() // 重新加载对应时间范围的数据
  },

  // 显示评分说明
  showScoreInfo() {
    this.setData({ showScoreInfo: true })
  },

  // 隐藏评分说明
  hideScoreInfo() {
    this.setData({ showScoreInfo: false })
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止事件冒泡
  },

  // 查看效率详情
  viewEfficiencyDetail() {
    wx.navigateTo({
      url: '/pages/statistics-detail/index?type=efficiency'
    })
  },

  // 查看科目详情
  viewSubjectsDetail() {
    wx.navigateTo({
      url: '/pages/statistics-detail/index?type=subjects'
    })
  },

  // 查看所有成就
  viewAllAchievements() {
    wx.navigateTo({
      url: '/pages/achievement-system/index'
    })
  },

  // 导出数据
  exportData() {
    wx.navigateTo({
      url: '/pages/data-export/index'
    })
  }
})