// pages/data-center/index.js
Page({
  data: {
    // 考试准备度
    preparationScore: 78,
    preparationLevel: '良好',
    preparationDescription: '继续保持，距离目标还有一步',
    scoreFactors: [],
    showScoreInfo: false,
    scoreFactorExplanations: [],

    // 复习统计
    currentTimeRange: 'week',
    timeRangeOptions: [],
    studyStats: [],

    // 科目分析
    subjectsData: [],

    // 成就
    recentAchievements: []
  },

  onLoad() {
    this.initPage()
  },

  onShow() {
    this.loadData()
  },

  // 初始化页面
  initPage() {
    this.initTimeRangeOptions()
    this.initScoreFactorExplanations()
    this.loadData()
  },

  // 初始化时间范围选项
  initTimeRangeOptions() {
    const timeRangeOptions = [
      { value: 'week', label: '本周' },
      { value: 'month', label: '本月' },
      { value: 'quarter', label: '本季度' },
      { value: 'year', label: '本年' }
    ]

    this.setData({ timeRangeOptions })
  },

  // 初始化评分因子说明
  initScoreFactorExplanations() {
    const scoreFactorExplanations = [
      {
        name: '复习时长',
        description: '每日复习时间的稳定性和充足性'
      },
      {
        name: '计划完成',
        description: '复习计划的完成率和质量'
      },
      {
        name: '知识掌握',
        description: '通过测试和练习反映的知识掌握程度'
      },
      {
        name: '复习效率',
        description: '单位时间内的复习效果和专注度'
      }
    ]

    this.setData({ scoreFactorExplanations })
  },

  // 加载数据
  loadData() {
    this.loadScoreFactors()
    this.loadStudyStats()
    this.loadSubjectsData()
    this.loadRecentAchievements()
  },

  // 加载评分因子
  loadScoreFactors() {
    const scoreFactors = [
      {
        name: '复习时长',
        score: 85,
        color: '#52C41A'
      },
      {
        name: '计划完成',
        score: 72,
        color: '#1890FF'
      },
      {
        name: '知识掌握',
        score: 68,
        color: '#FA8C16'
      },
      {
        name: '复习效率',
        score: 88,
        color: '#722ED1'
      }
    ]

    this.setData({ scoreFactors })
  },

  // 加载复习统计
  loadStudyStats() {
    const studyStats = [
      {
        icon: '⏰',
        label: '复习时长',
        value: '28.5h',
        bgColor: '#E6F7FF',
        trend: {
          icon: '📈',
          text: '+12%'
        }
      },
      {
        icon: '📝',
        label: '完成复习',
        value: '24个',
        bgColor: '#F6FFED',
        trend: {
          icon: '📈',
          text: '+8%'
        }
      },
      {
        icon: '🍅',
        label: '专注次数',
        value: '56次',
        bgColor: '#FFF7E6',
        trend: {
          icon: '📈',
          text: '+15%'
        }
      },
      {
        icon: '📊',
        label: '平均效率',
        value: '87%',
        bgColor: '#F9F0FF',
        trend: {
          icon: '📈',
          text: '+5%'
        }
      }
    ]

    this.setData({ studyStats })
  },



  // 加载科目数据
  loadSubjectsData() {
    const subjectsData = [
      {
        name: '数学',
        exam: '2025年考研',
        score: 85,
        progress: 75,
        progressColor: '#52C41A',
        studyTime: '45.2h',
        completedTasks: '18个',
        avgEfficiency: '88%'
      },
      {
        name: '英语',
        exam: '英语四级',
        score: 92,
        progress: 88,
        progressColor: '#52C41A',
        studyTime: '32.8h',
        completedTasks: '24个',
        avgEfficiency: '91%'
      },
      {
        name: '政治',
        exam: '2025年考研',
        score: 68,
        progress: 45,
        progressColor: '#FA8C16',
        studyTime: '18.5h',
        completedTasks: '8个',
        avgEfficiency: '72%'
      }
    ]

    this.setData({ subjectsData })
  },

  // 加载备考习惯
  loadStudyHabits() {
    const studyHabits = [
      {
        title: '最佳复习时段',
        icon: '🌅',
        value: '9:00-11:00',
        description: '效率最高的时间段'
      },
      {
        title: '平均专注时长',
        icon: '⏱️',
        value: '28分钟',
        description: '单次专注的平均时长'
      },
      {
        title: '备考连续天数',
        icon: '🔥',
        value: '15天',
        description: '当前连续备考记录'
      },
      {
        title: '最爱复习地点',
        icon: '📍',
        value: '图书馆',
        description: '效率最高的复习环境'
      }
    ]

    this.setData({ studyHabits })
  },

  // 加载最近成就
  loadRecentAchievements() {
    const recentAchievements = [
      {
        id: 'streak_15',
        icon: '🔥',
        name: '备考达人',
        description: '连续备考15天',
        time: '2小时前',
        isNew: true
      },
      {
        id: 'efficiency_90',
        icon: '⚡',
        name: '效率之星',
        description: '单日效率达到90%',
        time: '昨天',
        isNew: false
      },
      {
        id: 'pomodoro_50',
        icon: '🍅',
        name: '专注大师',
        description: '完成50次番茄钟',
        time: '3天前',
        isNew: false
      }
    ]

    this.setData({ recentAchievements })
  },

  // 切换时间范围
  switchTimeRange(e) {
    const range = e.currentTarget.dataset.range
    this.setData({ currentTimeRange: range })
    this.loadStudyStats() // 重新加载对应时间范围的数据
  },

  // 显示评分说明
  showScoreInfo() {
    this.setData({ showScoreInfo: true })
  },

  // 隐藏评分说明
  hideScoreInfo() {
    this.setData({ showScoreInfo: false })
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止事件冒泡
  },

  // 查看效率详情
  viewEfficiencyDetail() {
    wx.navigateTo({
      url: '/pages/statistics-detail/index?type=efficiency'
    })
  },

  // 查看科目详情
  viewSubjectsDetail() {
    wx.navigateTo({
      url: '/pages/statistics-detail/index?type=subjects'
    })
  },

  // 查看所有成就
  viewAllAchievements() {
    wx.navigateTo({
      url: '/pages/achievement-system/index'
    })
  },

  // 导出数据
  exportData() {
    wx.navigateTo({
      url: '/pages/data-export/index'
    })
  }
})