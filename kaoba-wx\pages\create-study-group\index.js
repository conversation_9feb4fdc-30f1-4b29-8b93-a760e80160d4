// pages/create-study-group/index.js
const SmartApi = require('../../utils/smartApi')

Page({
  data: {
    examList: [],
    selectedExam: null,
    groupName: '',
    loading: false
  },

  async onLoad() {
    await this.loadExamList()
  },

  // 加载考试列表
  async loadExamList() {
    try {
      const result = await SmartApi.getExams({}, 50, 0)
      
      if (result.success && result.data) {
        const examList = result.data.map(exam => ({
          id: exam._id,
          name: exam.title,
          date: exam.examDate,
          subject: exam.subject
        }))

        this.setData({ examList })
      }
    } catch (error) {
      console.error('加载考试列表失败:', error)
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      })
    }
  },

  // 选择考试
  onSelectExam(e) {
    const { exam } = e.currentTarget.dataset
    this.setData({ selectedExam: exam })
  },

  // 输入小组名称
  onGroupNameInput(e) {
    this.setData({ groupName: e.detail.value })
  },

  // 创建小组
  async onCreateGroup() {
    const { selectedExam, groupName } = this.data

    if (!selectedExam) {
      wx.showToast({
        title: '请选择考试',
        icon: 'none'
      })
      return
    }

    this.setData({ loading: true })

    try {
      const result = await SmartApi.createStudyGroup(
        selectedExam.id,
        selectedExam.name,
        groupName || null
      )

      if (result.success) {
        wx.showModal({
          title: '创建成功！',
          content: `邀请码：${result.data.inviteCode}\n\n分享给朋友一起备考吧！`,
          showCancel: false,
          confirmText: '分享邀请码',
          success: (res) => {
            if (res.confirm) {
              this.shareInviteCode(result.data.inviteCode, selectedExam.name)
            }
          }
        })

        // 延迟返回，让用户看到邀请码
        setTimeout(() => {
          wx.navigateBack()
        }, 2000)
      } else {
        wx.showToast({
          title: result.error || '创建失败',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('创建搭子小组失败:', error)
      wx.showToast({
        title: '创建失败，请重试',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  // 分享邀请码
  shareInviteCode(inviteCode, examName) {
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    })
  },

  // 分享给朋友
  onShareAppMessage() {
    const { selectedExam } = this.data
    return {
      title: `一起备考${selectedExam?.name || '考试'}吧！`,
      path: `/pages/join-study-group/index`,
      imageUrl: '/images/share-study-group.png'
    }
  }
})
