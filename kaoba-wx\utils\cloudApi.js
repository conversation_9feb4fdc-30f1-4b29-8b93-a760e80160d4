// 云开发API工具类
class CloudApi {
  // 任务管理相关API
  static async getTasks(filter = {}, limit = 20, skip = 0) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'taskManager',
        data: {
          action: 'getTasks',
          data: { filter, limit, skip }
        }
      })
      return result.result
    } catch (error) {
      console.error('获取任务列表失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async getTaskById(taskId) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'taskManager',
        data: {
          action: 'getTaskById',
          data: { taskId }
        }
      })
      return result.result
    } catch (error) {
      console.error('获取任务详情失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async addTask(taskData) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'taskManager',
        data: {
          action: 'addTask',
          data: taskData
        }
      })
      return result.result
    } catch (error) {
      console.error('添加任务失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async updateTask(taskId, updates) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'taskManager',
        data: {
          action: 'updateTask',
          data: { taskId, updates }
        }
      })
      return result.result
    } catch (error) {
      console.error('更新任务失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async deleteTask(taskId) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'taskManager',
        data: {
          action: 'deleteTask',
          data: { taskId }
        }
      })
      return result.result
    } catch (error) {
      console.error('删除任务失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async completeTask(taskId, completed = true) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'taskManager',
        data: {
          action: 'completeTask',
          data: { taskId, completed }
        }
      })
      return result.result
    } catch (error) {
      console.error('完成任务失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async getTaskStats(dateRange = null) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'taskManager',
        data: {
          action: 'getTaskStats',
          data: { dateRange }
        }
      })
      return result.result
    } catch (error) {
      console.error('获取任务统计失败:', error)
      return { success: false, error: error.message }
    }
  }

  // 考试管理相关API
  static async getExams(filter = {}, limit = 20, skip = 0) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'examManager',
        data: {
          action: 'getExams',
          data: { filter, limit, skip }
        }
      })
      return result.result
    } catch (error) {
      console.error('获取考试列表失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async getExamById(examId) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'examManager',
        data: {
          action: 'getExamById',
          data: { examId }
        }
      })
      return result.result
    } catch (error) {
      console.error('获取考试详情失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async addExam(examData) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'examManager',
        data: {
          action: 'addExam',
          data: examData
        }
      })
      return result.result
    } catch (error) {
      console.error('添加考试失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async updateExam(examId, updates) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'examManager',
        data: {
          action: 'updateExam',
          data: { examId, updates }
        }
      })
      return result.result
    } catch (error) {
      console.error('更新考试失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async deleteExam(examId) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'examManager',
        data: {
          action: 'deleteExam',
          data: { examId }
        }
      })
      return result.result
    } catch (error) {
      console.error('删除考试失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async getUpcomingExams(days = 7, limit = 10) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'examManager',
        data: {
          action: 'getUpcomingExams',
          data: { days, limit }
        }
      })
      return result.result
    } catch (error) {
      console.error('获取即将到来的考试失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async getExamStats(dateRange = null) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'examManager',
        data: {
          action: 'getExamStats',
          data: { dateRange }
        }
      })
      return result.result
    } catch (error) {
      console.error('获取考试统计失败:', error)
      return { success: false, error: error.message }
    }
  }

  // 学习管理相关API
  static async startStudySession(sessionData) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'studyManager',
        data: {
          action: 'startStudySession',
          data: sessionData
        }
      })
      return result.result
    } catch (error) {
      console.error('开始学习会话失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async endStudySession(sessionId, endTime = null, notes = '') {
    try {
      const result = await wx.cloud.callFunction({
        name: 'studyManager',
        data: {
          action: 'endStudySession',
          data: { sessionId, endTime, notes }
        }
      })
      return result.result
    } catch (error) {
      console.error('结束学习会话失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async getStudyStats(dateRange = null, groupBy = 'day') {
    try {
      const result = await wx.cloud.callFunction({
        name: 'studyManager',
        data: {
          action: 'getStudyStats',
          data: { dateRange, groupBy }
        }
      })
      return result.result
    } catch (error) {
      console.error('获取学习统计失败:', error)
      return { success: false, error: error.message }
    }
  }

  // 统计数据管理API
  static async getUserStats(timeRange = 'week') {
    try {
      const result = await wx.cloud.callFunction({
        name: 'statsManager',
        data: {
          action: 'getUserStats',
          data: { timeRange }
        }
      })
      return result.result
    } catch (error) {
      console.error('获取用户统计失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async updateDailyStats(statsData) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'statsManager',
        data: {
          action: 'updateDailyStats',
          data: statsData
        }
      })
      return result.result
    } catch (error) {
      console.error('更新每日统计失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async getDailyStats(date = null) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'statsManager',
        data: {
          action: 'getDailyStats',
          data: { date }
        }
      })
      return result.result
    } catch (error) {
      console.error('获取每日统计失败:', error)
      return { success: false, error: error.message }
    }
  }

  // 成就系统API
  static async getUserAchievements(category = null) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'achievementManager',
        data: {
          action: 'getUserAchievements',
          data: { category }
        }
      })
      return result.result
    } catch (error) {
      console.error('获取用户成就失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async checkAchievements(triggerType, value) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'achievementManager',
        data: {
          action: 'checkAchievements',
          data: { triggerType, value }
        }
      })
      return result.result
    } catch (error) {
      console.error('检查成就失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async getAchievementStats() {
    try {
      const result = await wx.cloud.callFunction({
        name: 'achievementManager',
        data: {
          action: 'getAchievementStats',
          data: {}
        }
      })
      return result.result
    } catch (error) {
      console.error('获取成就统计失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async addPomodoroSession(pomodoroData) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'studyManager',
        data: {
          action: 'addPomodoroSession',
          data: pomodoroData
        }
      })
      return result.result
    } catch (error) {
      console.error('添加番茄钟会话失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async getPomodoroStats(dateRange = null) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'studyManager',
        data: {
          action: 'getPomodoroStats',
          data: { dateRange }
        }
      })
      return result.result
    } catch (error) {
      console.error('获取番茄钟统计失败:', error)
      return { success: false, error: error.message }
    }
  }

  // 用户登录
  static async login() {
    try {
      const result = await wx.cloud.callFunction({
        name: 'login'
      })
      return result.result
    } catch (error) {
      console.error('登录失败:', error)
      return { success: false, error: error.message }
    }
  }
  // 备考搭子API
  static async createStudyGroup(examId, examName, groupName) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'studyGroupManager',
        data: {
          action: 'createGroup',
          data: { examId, examName, groupName }
        }
      })
      return result.result
    } catch (error) {
      console.error('创建搭子小组失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async joinStudyGroup(inviteCode) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'studyGroupManager',
        data: {
          action: 'joinGroup',
          data: { inviteCode }
        }
      })
      return result.result
    } catch (error) {
      console.error('加入搭子小组失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async leaveStudyGroup(groupId) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'studyGroupManager',
        data: {
          action: 'leaveGroup',
          data: { groupId }
        }
      })
      return result.result
    } catch (error) {
      console.error('退出搭子小组失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async getMyStudyGroups() {
    try {
      const result = await wx.cloud.callFunction({
        name: 'studyGroupManager',
        data: {
          action: 'getMyGroups',
          data: {}
        }
      })
      return result.result
    } catch (error) {
      console.error('获取我的搭子小组失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async getStudyGroupDetail(groupId) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'studyGroupManager',
        data: {
          action: 'getGroupDetail',
          data: { groupId }
        }
      })
      return result.result
    } catch (error) {
      console.error('获取搭子小组详情失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async shareGroupPlan(groupId, planData) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'studyGroupManager',
        data: {
          action: 'sharePlan',
          data: { groupId, planData }
        }
      })
      return result.result
    } catch (error) {
      console.error('分享小组计划失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async copyGroupPlan(shareId) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'studyGroupManager',
        data: {
          action: 'copyPlan',
          data: { shareId }
        }
      })
      return result.result
    } catch (error) {
      console.error('复制小组计划失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async likeGroupPlan(shareId) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'studyGroupManager',
        data: {
          action: 'likePlan',
          data: { shareId }
        }
      })
      return result.result
    } catch (error) {
      console.error('点赞小组计划失败:', error)
      return { success: false, error: error.message }
    }
  }

  // 测试搭子小组数据库
  static async testStudyGroupDatabase() {
    try {
      const result = await wx.cloud.callFunction({
        name: 'studyGroupManager',
        data: {
          action: 'testDatabase',
          data: {}
        }
      })
      return result.result
    } catch (error) {
      console.error('测试数据库失败:', error)
      return { success: false, error: error.message }
    }
  }
}

module.exports = CloudApi
