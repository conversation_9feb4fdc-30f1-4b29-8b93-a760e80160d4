// pages/task-detail/index.js
const SmartApi = require('../../utils/smartApi')

Page({
  data: {
    taskId: '',
    task: {},
    studyRecords: [],
    showActionSheet: false
  },

  onLoad(options) {
    const taskId = options.id
    if (taskId) {
      this.setData({ taskId })
      this.loadTaskDetail(taskId)
    }
  },

  onShow() {
    // 页面显示时刷新数据
    if (this.data.taskId) {
      this.loadTaskDetail(this.data.taskId)
    }
  },

  // 加载任务详情
  async loadTaskDetail(taskId) {
    wx.showLoading({
      title: '加载中...',
      mask: true
    })

    try {
      // 从数据库获取任务详情
      const taskResult = await SmartApi.getTaskById(taskId)

      if (taskResult.success && taskResult.data) {
        const taskData = taskResult.data

        // 处理任务数据
        const task = {
          id: taskData._id || taskData.id,
          title: taskData.title || '',
          description: taskData.description || '',
          subject: taskData.subject || '未分类',
          examName: taskData.examName || '',
          examId: taskData.examId || '',
          priority: taskData.priority || 'medium',
          priorityText: this.getPriorityText(taskData.priority),
          status: taskData.status || 'pending',
          statusText: this.getStatusText(taskData.status),
          progress: taskData.progress || 0,
          dueDate: this.formatDate(taskData.dueDate),
          dueTime: this.formatTime(taskData.dueTime),
          estimatedDuration: taskData.estimatedDuration || '',
          actualDuration: taskData.actualDuration || '',
          createTime: this.formatDateTime(taskData.createTime),
          completed: taskData.completed || false,
          subtasks: taskData.subtasks || [],
          tags: taskData.tags || []
        }

        // 计算子任务统计
        const completedSubtasks = task.subtasks.filter(sub => sub.completed).length
        task.completedSubtasks = completedSubtasks
        task.totalSubtasks = task.subtasks.length

        // 加载学习记录
        await this.loadStudyRecords(taskId)

        this.setData({ task })

        wx.hideLoading()
      } else {
        wx.hideLoading()
        wx.showToast({
          title: '任务不存在',
          icon: 'none'
        })
        setTimeout(() => {
          wx.navigateBack()
        }, 1500)
      }
    } catch (error) {
      wx.hideLoading()
      console.error('加载任务详情失败:', error)
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      })
    }
  },

  // 获取优先级文本
  getPriorityText(priority) {
    const priorityMap = {
      'high': '高优先级',
      'medium': '中优先级',
      'low': '低优先级'
    }
    return priorityMap[priority] || '中优先级'
  },

  // 获取状态文本
  getStatusText(status) {
    const statusMap = {
      'pending': '进行中',
      'completed': '已完成',
      'paused': '已暂停',
      'cancelled': '已取消'
    }
    return statusMap[status] || '进行中'
  },

  // 格式化日期
  formatDate(dateStr) {
    if (!dateStr) return ''
    const date = new Date(dateStr)
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
  },

  // 格式化时间
  formatTime(timeStr) {
    if (!timeStr) return ''
    if (timeStr.includes(':')) return timeStr
    // 如果是时间戳，转换为时间格式
    const date = new Date(timeStr)
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    return `${hours}:${minutes}`
  },

  // 格式化日期时间
  formatDateTime(dateTimeStr) {
    if (!dateTimeStr) return ''
    const date = new Date(dateTimeStr)
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    return `${year}-${month}-${day} ${hours}:${minutes}`
  },

  // 加载学习记录
  async loadStudyRecords(taskId) {
    try {
      // 这里可以调用获取学习记录的API
      // 暂时使用空数组，后续可以扩展
      const studyRecords = []
      this.setData({ studyRecords })
    } catch (error) {
      console.error('加载学习记录失败:', error)
      this.setData({ studyRecords: [] })
    }
  },

  // 编辑任务
  editTask() {
    wx.navigateTo({
      url: `/pages/edit-task/index?id=${this.data.taskId}`
    })
  },

  // 显示更多操作
  showMoreActions() {
    this.setData({ showActionSheet: true })
  },

  // 隐藏操作菜单
  hideActionSheet() {
    this.setData({ showActionSheet: false })
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止事件冒泡
  },

  // 切换子任务状态
  toggleSubtask(e) {
    const subtaskId = e.currentTarget.dataset.id
    const task = { ...this.data.task }

    task.subtasks = task.subtasks.map(subtask => {
      if (subtask.id === subtaskId) {
        subtask.completed = !subtask.completed
        if (subtask.completed) {
          subtask.completedTime = new Date().toLocaleString()
        } else {
          delete subtask.completedTime
        }
      }
      return subtask
    })

    // 重新计算进度
    const completedCount = task.subtasks.filter(s => s.completed).length
    task.completedSubtasks = completedCount
    task.progress = Math.round((completedCount / task.subtasks.length) * 100)

    this.setData({ task })

    wx.showToast({
      title: task.subtasks.find(s => s.id === subtaskId).completed ? '子任务完成' : '子任务重新开始',
      icon: 'success'
    })
  },

  // 开始番茄钟
  startPomodoro() {
    wx.switchTab({
      url: '/pages/pomodoro/index'
    })
  },

  // 完成任务
  completeTask() {
    wx.showModal({
      title: '确认完成',
      content: '确定要标记此任务为已完成吗？',
      success: (res) => {
        if (res.confirm) {
          const task = { ...this.data.task }
          task.completed = true
          task.status = 'completed'
          task.statusText = '已完成'
          task.progress = 100

          this.setData({ task })

          wx.showToast({
            title: '任务已完成',
            icon: 'success'
          })
        }
      }
    })
  },

  // 分享任务
  shareTask() {
    wx.showShareMenu({
      withShareTicket: true
    })
  },

  // 复制任务
  duplicateTask() {
    this.hideActionSheet()
    wx.navigateTo({
      url: `/pages/add-task/index?duplicate=${this.data.taskId}`
    })
  },

  // 删除任务
  deleteTask() {
    this.hideActionSheet()
    wx.showModal({
      title: '确认删除',
      content: '确定要删除此任务吗？此操作不可恢复。',
      confirmText: '删除',
      confirmColor: '#FF4D4F',
      success: (res) => {
        if (res.confirm) {
          wx.showToast({
            title: '任务已删除',
            icon: 'success'
          })

          setTimeout(() => {
            wx.navigateBack()
          }, 1500)
        }
      }
    })
  }
})