<!--pages/add-task/index.wxml-->
<view class="container">
  <form bindsubmit="submitTask">
    <!-- 复习计划基本信息 -->
    <view class="form-section">
      <text class="section-title">基本信息</text>

      <view class="form-item">
        <text class="form-label">复习内容 *</text>
        <input class="form-input"
               placeholder="请输入复习内容"
               value="{{taskForm.title}}"
               bindinput="updateTitle"
               maxlength="50"/>
      </view>

      <view class="form-item">
        <text class="form-label">复习说明</text>
        <textarea class="form-textarea"
                  placeholder="详细描述复习内容和要求..."
                  value="{{taskForm.description}}"
                  bindinput="updateDescription"
                  maxlength="200"/>
      </view>
    </view>

    <!-- 复习分类 -->
    <view class="form-section">
      <text class="section-title">复习分类</text>

      <view class="form-item">
        <text class="form-label">关联考试</text>
        <view class="selector" bindtap="selectExam">
          <text class="selector-text">{{taskForm.examName || '选择考试'}}</text>
          <text class="selector-arrow">›</text>
        </view>
      </view>

      <view class="form-item">
        <text class="form-label">学科</text>
        <view class="subject-tags">
          <view class="subject-tag {{taskForm.subject === item ? 'active' : ''}}"
                wx:for="{{subjectOptions}}"
                wx:key="*this"
                bindtap="selectSubject"
                data-subject="{{item}}">
            {{item}}
          </view>
        </view>
      </view>
    </view>

    <!-- 复习设置 -->
    <view class="form-section">
      <text class="section-title">复习设置</text>

      <view class="form-item">
        <text class="form-label">优先级</text>
        <view class="priority-options">
          <view class="priority-option {{taskForm.priority === item.value ? 'active' : ''}}"
                wx:for="{{priorityOptions}}"
                wx:key="value"
                bindtap="selectPriority"
                data-priority="{{item.value}}">
            <text class="priority-icon" style="color: {{item.color}}">{{item.icon}}</text>
            <text class="priority-text">{{item.label}}</text>
          </view>
        </view>
      </view>

      <view class="form-item">
        <text class="form-label">截止时间</text>
        <view class="datetime-picker">
          <picker mode="date"
                  value="{{taskForm.dueDate}}"
                  bindchange="selectDueDate">
            <view class="picker-item">
              <text>{{taskForm.dueDate || '选择日期'}}</text>
              <text class="picker-icon">📅</text>
            </view>
          </picker>
          <picker mode="time"
                  value="{{taskForm.dueTime}}"
                  bindchange="selectDueTime">
            <view class="picker-item">
              <text>{{taskForm.dueTime || '选择时间'}}</text>
              <text class="picker-icon">⏰</text>
            </view>
          </picker>
        </view>
      </view>

      <view class="form-item">
        <text class="form-label">预计时长</text>
        <view class="duration-selector">
          <view class="duration-option {{taskForm.estimatedDuration === item ? 'active' : ''}}"
                wx:for="{{durationOptions}}"
                wx:key="*this"
                bindtap="selectDuration"
                data-duration="{{item}}">
            {{item}}
          </view>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="form-actions">
      <button class="cancel-btn" bindtap="cancelTask">取消</button>
      <button class="submit-btn" formType="submit">创建复习计划</button>
    </view>
  </form>
</view>

<!-- 考试选择弹窗 -->
<view class="exam-modal" wx:if="{{showExamModal}}" bindtap="hideExamModal">
  <view class="modal-content" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">选择考试</text>
      <text class="modal-close" bindtap="hideExamModal">×</text>
    </view>
    <view class="modal-body">
      <view class="exam-option"
            wx:for="{{examOptions}}"
            wx:key="id"
            bindtap="selectExamOption"
            data-exam="{{item}}">
        <text class="exam-name">{{item.name}}</text>
        <text class="exam-date">{{item.date}}</text>
      </view>
    </view>
  </view>
</view>