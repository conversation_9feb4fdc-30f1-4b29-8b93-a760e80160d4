<!--pages/profile/index.wxml-->
<view class="container">
  <!-- 用户信息 -->
  <view class="user-info-container">
    <!-- 已登录状态 -->
    <view class="user-profile" wx:if="{{isLoggedIn}}">
      <avatar-upload
        avatarUrl="{{userInfo.avatarUrl}}"
        size="{{120}}"
        editable="{{true}}"
        showEditButton="{{false}}"
        bind:avatarChange="onAvatarChange"
      ></avatar-upload>
      <view class="user-details">
        <text class="user-name">{{userInfo.nickName || '考试达人'}}</text>
        <text class="user-exam">{{userInfo.currentExam || '暂未设置考试'}}</text>
        <text class="login-status">已登录 {{userInfo.isGuest ? '(游客模式)' : ''}}</text>
      </view>
      <button class="edit-profile-btn" bindtap="editProfile">编辑</button>
    </view>

    <!-- 未登录状态 -->
    <view class="user-profile login-prompt" wx:else>
      <image class="user-avatar" src="/images/default-avatar.png" mode="aspectFill"></image>
      <view class="user-details">
        <text class="user-name">未登录</text>
        <text class="user-exam">登录后可同步学习数据</text>
      </view>
      <button class="login-btn" bindtap="goToLogin">登录</button>
    </view>

    <view class="user-stats">
      <view class="stat-item" wx:for="{{userStats}}" wx:key="label">
        <text class="stat-value">{{item.value}}</text>
        <text class="stat-label">{{item.label}}</text>
      </view>
    </view>
  </view>

  <!-- 学习成就 -->
  <view class="achievements-container">
    <view class="section-header">
      <text class="section-title">学习成就</text>
      <text class="view-all" bindtap="viewAllAchievements">查看全部</text>
    </view>
    <view class="achievements-grid">
      <view class="achievement-item" wx:for="{{recentAchievements}}" wx:key="id">
        <text class="achievement-icon">{{item.icon}}</text>
        <text class="achievement-name">{{item.name}}</text>
      </view>
    </view>
  </view>

  <!-- 功能菜单 -->
  <view class="menu-container">
    <view class="menu-section" wx:for="{{menuSections}}" wx:key="title">
      <text class="menu-section-title">{{item.title}}</text>
      <view class="menu-items">
        <view class="menu-item"
              wx:for="{{item.items}}"
              wx:key="id"
              wx:for-item="menuItem"
              bindtap="handleMenuTap"
              data-action="{{menuItem.action}}">
          <view class="menu-item-left">
            <text class="menu-icon">{{menuItem.icon}}</text>
            <text class="menu-text">{{menuItem.text}}</text>
          </view>
          <view class="menu-item-right">
            <text class="menu-badge" wx:if="{{menuItem.badge}}">{{menuItem.badge}}</text>
            <text class="menu-arrow">›</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 数据统计卡片 -->
  <view class="data-cards-container">
    <view class="data-card" wx:for="{{dataCards}}" wx:key="id" bindtap="viewDataDetail" data-type="{{item.type}}">
      <view class="card-header">
        <text class="card-title">{{item.title}}</text>
        <text class="card-icon">{{item.icon}}</text>
      </view>
      <view class="card-content">
        <text class="card-value">{{item.value}}</text>
        <text class="card-description">{{item.description}}</text>
      </view>
      <view class="card-trend" wx:if="{{item.trend}}">
        <text class="trend-text trend-{{item.trend.type}}">{{item.trend.text}}</text>
      </view>
    </view>
  </view>

  <!-- 快捷操作 -->
  <view class="quick-actions-container">
    <text class="section-title">快捷操作</text>
    <view class="quick-actions-grid">
      <view class="quick-action-item"
            wx:for="{{quickActions}}"
            wx:key="id"
            bindtap="handleQuickAction"
            data-action="{{item.action}}">
        <view class="action-icon" style="background-color: {{item.bgColor}}">
          <text>{{item.icon}}</text>
        </view>
        <text class="action-text">{{item.text}}</text>
      </view>
    </view>
  </view>

  <!-- 版本信息 -->
  <view class="version-info">
    <text class="version-text">要考试啦 v{{version}}</text>
    <text class="copyright">© 2025 考试助手</text>
  </view>
</view>

<!-- 用户信息编辑弹窗 -->
<view class="edit-modal-mask" wx:if="{{showEditModal}}" bindtap="hideEditModal">
  <view class="edit-modal" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">编辑个人信息</text>
      <text class="modal-close" bindtap="hideEditModal">×</text>
    </view>
    <view class="modal-body">
      <view class="form-item avatar-item">
        <text class="form-label">头像</text>
        <avatar-upload
          avatarUrl="{{editUserInfo.avatarUrl}}"
          size="{{80}}"
          editable="{{true}}"
          showEditButton="{{true}}"
          bind:avatarChange="onEditAvatarChange"
        ></avatar-upload>
      </view>
      <view class="form-item">
        <text class="form-label">昵称</text>
        <input class="form-input"
               placeholder="请输入昵称"
               value="{{editUserInfo.nickname}}"
               bindinput="updateNickname"
               maxlength="20"/>
      </view>
      <view class="form-item">
        <text class="form-label">当前考试</text>
        <input class="form-input"
               placeholder="请输入当前准备的考试"
               value="{{editUserInfo.currentExam}}"
               bindinput="updateCurrentExam"
               maxlength="50"/>
      </view>
      <view class="form-item">
        <text class="form-label">个性签名</text>
        <textarea class="form-textarea"
                  placeholder="写点什么激励自己..."
                  value="{{editUserInfo.signature}}"
                  bindinput="updateSignature"
                  maxlength="100"/>
      </view>
    </view>
    <view class="modal-footer">
      <button class="modal-btn cancel-btn" bindtap="hideEditModal">取消</button>
      <button class="modal-btn confirm-btn" bindtap="saveUserInfo">保存</button>
    </view>
  </view>
</view>