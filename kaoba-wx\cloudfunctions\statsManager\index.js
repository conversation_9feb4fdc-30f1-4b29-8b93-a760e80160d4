// 统计数据管理云函数
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

exports.main = async (event, context) => {
  const { action, data } = event
  const { OPENID: userId } = cloud.getWXContext()

  console.log('statsManager云函数调用:', { action, userId })

  try {
    switch (action) {
      case 'getUserStats':
        return await getUserStats(userId, data)
      case 'updateDailyStats':
        return await updateDailyStats(userId, data)
      case 'getDailyStats':
        return await getDailyStats(userId, data)
      case 'getStatsRange':
        return await getStatsRange(userId, data)
      case 'calculateEfficiency':
        return await calculateEfficiency(userId, data)
      default:
        return { success: false, error: '未知操作' }
    }
  } catch (error) {
    console.error('statsManager云函数执行失败:', error)
    return { success: false, error: error.message }
  }
}

// 获取用户统计数据
async function getUserStats(userId, params) {
  const { timeRange = 'week' } = params || {}
  
  try {
    const endDate = new Date()
    const startDate = new Date()
    
    // 根据时间范围设置开始日期
    switch (timeRange) {
      case 'day':
        startDate.setDate(endDate.getDate() - 1)
        break
      case 'week':
        startDate.setDate(endDate.getDate() - 7)
        break
      case 'month':
        startDate.setMonth(endDate.getMonth() - 1)
        break
      case 'year':
        startDate.setFullYear(endDate.getFullYear() - 1)
        break
    }

    // 获取时间范围内的统计数据
    const result = await db.collection('daily_stats')
      .where({
        userId: userId,
        date: db.command.gte(startDate.toISOString().split('T')[0])
          .and(db.command.lte(endDate.toISOString().split('T')[0]))
      })
      .orderBy('date', 'desc')
      .get()

    // 计算汇总统计
    const stats = result.data.reduce((acc, day) => {
      acc.totalStudyTime += day.studyTime || 0
      acc.totalTasks += day.completedTasks || 0
      acc.totalPomodoros += day.pomodoroSessions || 0
      acc.totalDays += 1
      acc.efficiencySum += day.efficiency || 0
      return acc
    }, {
      totalStudyTime: 0,
      totalTasks: 0,
      totalPomodoros: 0,
      totalDays: 0,
      efficiencySum: 0
    })

    // 格式化返回数据
    const userStats = {
      studyTime: formatDuration(stats.totalStudyTime),
      completedTasks: stats.totalTasks,
      pomodoroCount: stats.totalPomodoros,
      averageEfficiency: stats.totalDays > 0 ? Math.round(stats.efficiencySum / stats.totalDays) : 0,
      studyDays: stats.totalDays,
      dailyData: result.data
    }

    return { success: true, data: userStats }
  } catch (error) {
    console.error('获取用户统计失败:', error)
    return { success: false, error: '获取统计数据失败' }
  }
}

// 更新每日统计
async function updateDailyStats(userId, params) {
  const { 
    studyTime = 0, 
    completedTasks = 0, 
    pomodoroSessions = 0, 
    efficiency = 0,
    subject = null 
  } = params || {}
  
  const today = new Date().toISOString().split('T')[0]
  
  try {
    // 查找今日统计记录
    const existingResult = await db.collection('daily_stats')
      .where({
        userId: userId,
        date: today
      })
      .get()

    if (existingResult.data.length > 0) {
      // 更新现有记录
      const existing = existingResult.data[0]
      const updateData = {
        studyTime: (existing.studyTime || 0) + studyTime,
        completedTasks: (existing.completedTasks || 0) + completedTasks,
        pomodoroSessions: (existing.pomodoroSessions || 0) + pomodoroSessions,
        efficiency: efficiency > 0 ? Math.round(((existing.efficiency || 0) + efficiency) / 2) : existing.efficiency,
        updateTime: new Date().toISOString()
      }

      // 更新科目统计
      if (subject) {
        const subjects = existing.subjects || {}
        subjects[subject] = subjects[subject] || { studyTime: 0, tasks: 0 }
        subjects[subject].studyTime += studyTime
        subjects[subject].tasks += completedTasks
        updateData.subjects = subjects
      }

      await db.collection('daily_stats').doc(existing._id).update({
        data: updateData
      })
    } else {
      // 创建新记录
      const newRecord = {
        userId: userId,
        date: today,
        studyTime: studyTime,
        completedTasks: completedTasks,
        pomodoroSessions: pomodoroSessions,
        efficiency: efficiency,
        subjects: subject ? { [subject]: { studyTime, tasks: completedTasks } } : {},
        createTime: new Date().toISOString(),
        updateTime: new Date().toISOString()
      }

      await db.collection('daily_stats').add({
        data: newRecord
      })
    }

    return { success: true }
  } catch (error) {
    console.error('更新每日统计失败:', error)
    return { success: false, error: '更新统计失败' }
  }
}

// 获取每日统计
async function getDailyStats(userId, params) {
  const { date = new Date().toISOString().split('T')[0] } = params || {}
  
  try {
    const result = await db.collection('daily_stats')
      .where({
        userId: userId,
        date: date
      })
      .get()

    const data = result.data.length > 0 ? result.data[0] : {
      studyTime: 0,
      completedTasks: 0,
      pomodoroSessions: 0,
      efficiency: 0,
      subjects: {}
    }

    return { success: true, data }
  } catch (error) {
    console.error('获取每日统计失败:', error)
    return { success: false, error: '获取统计失败' }
  }
}

// 获取时间范围统计
async function getStatsRange(userId, params) {
  const { startDate, endDate, groupBy = 'day' } = params || {}
  
  try {
    const result = await db.collection('daily_stats')
      .where({
        userId: userId,
        date: db.command.gte(startDate).and(db.command.lte(endDate))
      })
      .orderBy('date', 'asc')
      .get()

    return { success: true, data: result.data }
  } catch (error) {
    console.error('获取时间范围统计失败:', error)
    return { success: false, error: '获取统计失败' }
  }
}

// 计算效率
async function calculateEfficiency(userId, params) {
  const { taskId, duration, completed } = params || {}
  
  // 简单的效率计算逻辑
  let efficiency = 50 // 基础效率
  
  if (completed) {
    efficiency += 30 // 完成任务加分
  }
  
  if (duration && duration > 0) {
    // 根据专注时长调整效率
    const focusMinutes = duration / 60
    if (focusMinutes >= 25) {
      efficiency += 20 // 专注时间充足
    } else if (focusMinutes >= 15) {
      efficiency += 10
    }
  }
  
  efficiency = Math.min(100, Math.max(0, efficiency))
  
  return { success: true, data: { efficiency } }
}

// 格式化时长
function formatDuration(seconds) {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  
  if (hours > 0) {
    return `${hours}h${minutes > 0 ? minutes + 'm' : ''}`
  } else {
    return `${minutes}m`
  }
}
