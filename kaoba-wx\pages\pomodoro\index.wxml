<!--pages/pomodoro/index.wxml - 重新设计版本-->
<view class="container {{focusMode ? 'focus-mode' : ''}}">
  
  <!-- 普通模式布局 -->
  <view class="normal-layout" wx:if="{{!focusMode}}">
    
    <!-- 简洁头部 -->
    <view class="header">
      <text class="page-title">🍅 专注复习</text>
      <button class="settings-btn" bindtap="showSettings">⚙️</button>
    </view>

    <!-- 任务信息区域 -->
    <view class="task-info-area" bindtap="showTaskSelector">
      <view class="current-task" wx:if="{{selectedTask}}">
        <text class="task-name">{{selectedTask.title}}</text>
        <text class="task-progress">🍅 {{selectedTask.completedPomodoros || 0}}/{{selectedTask.totalPomodoros || 1}}</text>
      </view>
      <view class="no-task" wx:else>
        <text class="select-hint">点击选择复习内容</text>
        <text class="quick-mode-hint">或直接开始快速专注</text>
      </view>
    </view>

    <!-- 主计时器区域 -->
    <view class="main-timer-area">
      <!-- 计时器圆环 -->
      <view class="timer-circle {{isRunning ? 'breathing' : ''}}"
            style="background: conic-gradient({{timerColor}} 0deg {{progressDegree}}deg, rgba(0,0,0,0.1) {{progressDegree}}deg 360deg)"
            bindlongpress="enterFocusMode">
        <view class="timer-inner">
          <text class="time-display">{{displayTime}}</text>
          <text class="session-type">{{sessionTypeText}}</text>
        </view>
      </view>

      <!-- 主要控制按钮 -->
      <view class="primary-controls">
        <button class="main-control-btn {{isRunning ? 'pause' : 'start'}}" 
                bindtap="toggleTimer">
          {{isRunning ? '⏸️' : '▶️'}}
        </button>
        <button class="stop-btn" wx:if="{{isRunning || isPaused}}" bindtap="stopTimer">
          🛑
        </button>
      </view>
    </view>

    <!-- 底部快捷操作 -->
    <view class="bottom-actions">
      <button class="action-btn" bindtap="showTaskSelector">
        <text class="action-icon">📋</text>
        <text class="action-label">任务</text>
      </button>
      
      <button class="action-btn" bindtap="enterFocusMode">
        <text class="action-icon">🖥️</text>
        <text class="action-label">专注</text>
      </button>
      
      <button class="action-btn" bindtap="showSoundSettings">
        <text class="action-icon">🔊</text>
        <text class="action-label">声音</text>
      </button>
      
      <button class="action-btn" bindtap="viewStatistics">
        <text class="action-icon">📊</text>
        <text class="action-label">统计</text>
      </button>
    </view>

  </view>

  <!-- 专注模式布局 -->
  <view class="focus-layout" wx:if="{{focusMode}}">
    <!-- 专注模式头部 -->
    <view class="focus-header">
      <button class="exit-focus-btn" bindtap="exitFocusMode">×</button>
      <text class="focus-title">🌙 深度专注</text>
      <view class="focus-placeholder"></view>
    </view>

    <!-- 专注模式主体 -->
    <view class="focus-main">
      <!-- 专注模式计时器 -->
      <view class="focus-timer-display">
        <view class="focus-timer-circle {{isRunning ? 'breathing' : ''}}"
              style="background: conic-gradient({{timerColor}} 0deg {{progressDegree}}deg, rgba(255,255,255,0.1) {{progressDegree}}deg 360deg)">
          <view class="focus-timer-inner">
            <text class="focus-time-text">{{displayTime}}</text>
            <text class="focus-session-indicator">{{sessionIndicator}}</text>
          </view>
        </view>
      </view>

      <!-- 专注模式任务信息 -->
      <view class="focus-task-info" wx:if="{{currentSession}}">
        <text class="focus-task-title">{{currentSession.title}}</text>
        <text class="focus-task-subtitle">{{currentSession.subtitle}}</text>
      </view>

      <!-- 专注模式控制 -->
      <view class="focus-controls">
        <button class="focus-control-btn" bindtap="toggleTimer">
          {{isRunning ? '⏸️' : '▶️'}}
        </button>
        <button class="focus-stop-btn" bindtap="stopTimer">🛑</button>
      </view>
    </view>
  </view>
</view>

<!-- 任务选择弹窗 -->
<view class="modal-mask" wx:if="{{showTaskModal}}" bindtap="hideTaskModal">
  <view class="task-modal" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">选择复习内容</text>
      <text class="modal-close" bindtap="hideTaskModal">×</text>
    </view>
    <view class="modal-body">
      <!-- 快速专注选项 -->
      <view class="task-option quick-focus" bindtap="selectTaskOption" data-task="">
        <text class="task-icon">⚡</text>
        <view class="task-content">
          <text class="task-name">快速专注</text>
          <text class="task-desc">25分钟专注复习</text>
        </view>
      </view>
      
      <!-- 任务列表 -->
      <view class="task-option" 
            wx:for="{{availableTasks}}" 
            wx:key="id"
            bindtap="selectTaskOption" 
            data-task="{{item}}">
        <text class="task-icon">📚</text>
        <view class="task-content">
          <text class="task-name">{{item.title}}</text>
          <text class="task-desc">{{item.subject}}</text>
        </view>
      </view>
    </view>
  </view>
</view>

<!-- 声音设置弹窗 -->
<view class="modal-mask" wx:if="{{showSoundModal}}" bindtap="hideSoundModal">
  <view class="sound-modal" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">声音设置</text>
      <text class="modal-close" bindtap="hideSoundModal">×</text>
    </view>
    <view class="modal-body">
      <!-- 背景音选择 -->
      <view class="sound-section">
        <text class="section-title">背景音</text>
        <view class="sound-grid">
          <view class="sound-card {{selectedBgSound === sound.id ? 'active' : ''}}"
                wx:for="{{backgroundSounds}}"
                wx:for-item="sound"
                wx:key="id"
                bindtap="selectBackgroundSound"
                data-sound="{{sound.id}}">
            <text class="sound-icon">{{sound.icon}}</text>
            <text class="sound-name">{{sound.name}}</text>
          </view>
        </view>
      </view>
      
      <!-- 音量控制 -->
      <view class="sound-section">
        <text class="section-title">音量</text>
        <view class="volume-control">
          <slider value="{{bgVolume}}" min="0" max="100" bindchange="adjustBgVolume"/>
          <text class="volume-text">{{bgVolume}}%</text>
        </view>
      </view>
    </view>
  </view>
</view>

<!-- 设置弹窗 -->
<view class="modal-mask" wx:if="{{showSettings}}" bindtap="hideSettings">
  <view class="settings-modal" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">设置</text>
      <text class="modal-close" bindtap="hideSettings">×</text>
    </view>
    <view class="modal-body">
      <view class="setting-item">
        <text class="setting-label">工作时长</text>
        <view class="time-adjuster">
          <button class="time-btn" bindtap="adjustTime" data-type="work" data-action="decrease">-</button>
          <text class="time-value">{{workDuration}}分钟</text>
          <button class="time-btn" bindtap="adjustTime" data-type="work" data-action="increase">+</button>
        </view>
      </view>
      
      <view class="setting-item">
        <text class="setting-label">短休息</text>
        <view class="time-adjuster">
          <button class="time-btn" bindtap="adjustTime" data-type="shortBreak" data-action="decrease">-</button>
          <text class="time-value">{{shortBreakDuration}}分钟</text>
          <button class="time-btn" bindtap="adjustTime" data-type="shortBreak" data-action="increase">+</button>
        </view>
      </view>
      
      <view class="setting-item">
        <text class="setting-label">长休息</text>
        <view class="time-adjuster">
          <button class="time-btn" bindtap="adjustTime" data-type="longBreak" data-action="decrease">-</button>
          <text class="time-value">{{longBreakDuration}}分钟</text>
          <button class="time-btn" bindtap="adjustTime" data-type="longBreak" data-action="increase">+</button>
        </view>
      </view>
    </view>
  </view>
</view>
