<!--components/avatar-upload/index.wxml-->
<view class="avatar-upload-container">
  <!-- 头像显示区域 -->
  <view 
    class="avatar-wrapper {{editable ? 'editable' : ''}}" 
    style="width: {{size}}rpx; height: {{size}}rpx;"
    bindtap="{{editable ? 'chooseAvatar' : 'previewAvatar'}}"
  >
    <!-- 头像图片 -->
    <image 
      class="avatar-image" 
      src="{{avatarUrl || defaultAvatar}}" 
      mode="aspectFill"
      lazy-load="{{true}}"
    ></image>
    
    <!-- 上传中遮罩 -->
    <view class="upload-mask" wx:if="{{uploading}}">
      <view class="upload-loading">
        <text class="loading-icon">⏳</text>
        <text class="loading-text">上传中...</text>
      </view>
    </view>
    
    <!-- 编辑按钮 -->
    <view class="edit-button" wx:if="{{editable && showEditButton && !uploading}}">
      <text class="edit-icon">📷</text>
    </view>
    
    <!-- 可编辑提示 -->
    <view class="edit-hint" wx:if="{{editable && !uploading}}">
      <text>点击更换</text>
    </view>
  </view>
  
  <!-- 操作按钮组（可选） -->
  <view class="action-buttons" wx:if="{{editable && showEditButton}}">
    <button
      class="action-btn upload-btn {{uploading ? 'button-disabled' : ''}}"
      bindtap="chooseAvatar"
      disabled="{{uploading}}"
      size="mini"
    >
      <text wx:if="{{!uploading}}">更换头像</text>
      <text wx:else>上传中...</text>
    </button>

    <button
      class="action-btn delete-btn {{(uploading || !avatarUrl || avatarUrl === defaultAvatar) ? 'button-disabled' : ''}}"
      bindtap="deleteAvatar"
      disabled="{{uploading || !avatarUrl || avatarUrl === defaultAvatar}}"
      size="mini"
    >
      删除头像
    </button>
  </view>
</view>
