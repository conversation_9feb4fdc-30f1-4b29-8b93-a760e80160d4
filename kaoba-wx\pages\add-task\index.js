// pages/add-task/index.js
const SmartApi = require('../../utils/smartApi')

Page({
  data: {
    taskForm: {
      title: '',
      description: '',
      examId: '',
      examName: '',
      subject: '',
      priority: 'medium',
      dueDate: '',
      dueTime: '',
      estimatedDuration: '30分钟',
      subtasks: [],
      reminderEnabled: true,
      reminderTime: '1hour'
    },

    subjectOptions: ['数学', '英语', '政治', '专业课', '语文', '物理', '化学', '生物', '历史', '地理'],

    priorityOptions: [
      { value: 'high', label: '高优先级', icon: '🔴', color: '#FF4D4F' },
      { value: 'medium', label: '中优先级', icon: '🟡', color: '#FA8C16' },
      { value: 'low', label: '低优先级', icon: '🟢', color: '#52C41A' }
    ],

    durationOptions: ['15分钟', '30分钟', '45分钟', '1小时', '1.5小时', '2小时', '3小时', '4小时'],

    reminderOptions: [
      { value: '5min', label: '5分钟前' },
      { value: '15min', label: '15分钟前' },
      { value: '30min', label: '30分钟前' },
      { value: '1hour', label: '1小时前' },
      { value: '1day', label: '1天前' }
    ],

    examOptions: [],
    showExamModal: false
  },

  onLoad() {
    this.initPage()
  },

  // 初始化页面
  initPage() {
    this.loadExamOptions()
    this.setDefaultDateTime()
  },

  // 加载考试选项
  loadExamOptions() {
    // 模拟数据，实际应该从存储中获取
    const examOptions = [
      { id: 'exam_001', name: '2025年考研', date: '2025-12-23' },
      { id: 'exam_002', name: '英语四级', date: '2025-06-15' },
      { id: 'exam_003', name: '计算机二级', date: '2025-03-28' }
    ]

    this.setData({ examOptions })
  },

  // 设置默认日期时间
  setDefaultDateTime() {
    const now = new Date()
    const tomorrow = new Date(now.getTime() + 24 * 60 * 60 * 1000)

    const defaultDate = tomorrow.toISOString().split('T')[0]
    const defaultTime = '18:00'

    this.setData({
      'taskForm.dueDate': defaultDate,
      'taskForm.dueTime': defaultTime
    })
  },

  // 更新任务标题
  updateTitle(e) {
    this.setData({
      'taskForm.title': e.detail.value
    })
  },

  // 更新任务描述
  updateDescription(e) {
    this.setData({
      'taskForm.description': e.detail.value
    })
  },

  // 选择考试
  selectExam() {
    this.setData({ showExamModal: true })
  },

  // 隐藏考试选择弹窗
  hideExamModal() {
    this.setData({ showExamModal: false })
  },

  // 选择考试选项
  selectExamOption(e) {
    const exam = e.currentTarget.dataset.exam
    this.setData({
      'taskForm.examId': exam.id,
      'taskForm.examName': exam.name,
      showExamModal: false
    })
  },

  // 选择学科
  selectSubject(e) {
    const subject = e.currentTarget.dataset.subject
    this.setData({
      'taskForm.subject': subject
    })
  },

  // 选择优先级
  selectPriority(e) {
    const priority = e.currentTarget.dataset.priority
    this.setData({
      'taskForm.priority': priority
    })
  },

  // 选择截止日期
  selectDueDate(e) {
    this.setData({
      'taskForm.dueDate': e.detail.value
    })
  },

  // 选择截止时间
  selectDueTime(e) {
    this.setData({
      'taskForm.dueTime': e.detail.value
    })
  },

  // 选择预计时长
  selectDuration(e) {
    const duration = e.currentTarget.dataset.duration
    this.setData({
      'taskForm.estimatedDuration': duration
    })
  },

  // 添加子任务
  addSubtask() {
    const subtasks = [...this.data.taskForm.subtasks, '']
    this.setData({
      'taskForm.subtasks': subtasks
    })
  },

  // 更新子任务
  updateSubtask(e) {
    const index = e.currentTarget.dataset.index
    const value = e.detail.value
    const subtasks = [...this.data.taskForm.subtasks]
    subtasks[index] = value

    this.setData({
      'taskForm.subtasks': subtasks
    })
  },

  // 删除子任务
  removeSubtask(e) {
    const index = e.currentTarget.dataset.index
    const subtasks = [...this.data.taskForm.subtasks]
    subtasks.splice(index, 1)

    this.setData({
      'taskForm.subtasks': subtasks
    })
  },

  // 切换提醒开关
  toggleReminder(e) {
    this.setData({
      'taskForm.reminderEnabled': e.detail.value
    })
  },

  // 选择提醒时间
  selectReminderTime(e) {
    const time = e.currentTarget.dataset.time
    this.setData({
      'taskForm.reminderTime': time
    })
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止事件冒泡
  },

  // 取消创建任务
  cancelTask() {
    wx.showModal({
      title: '确认取消',
      content: '确定要取消创建任务吗？已填写的内容将丢失。',
      success: (res) => {
        if (res.confirm) {
          wx.navigateBack()
        }
      }
    })
  },

  // 提交任务
  submitTask() {
    const { taskForm } = this.data

    // 验证必填字段
    if (!taskForm.title || !taskForm.title.trim()) {
      wx.showToast({
        title: '请输入任务标题',
        icon: 'none'
      })
      return
    }

    if (!taskForm.subject) {
      wx.showToast({
        title: '请选择学科',
        icon: 'none'
      })
      return
    }

    // 生成任务ID
    const taskId = 'task_' + Date.now()

    // 构建任务数据
    const taskData = {
      id: taskId,
      title: taskForm.title.trim(),
      description: taskForm.description.trim(),
      examId: taskForm.examId,
      examName: taskForm.examName,
      subject: taskForm.subject,
      priority: taskForm.priority,
      dueDate: taskForm.dueDate,
      dueTime: taskForm.dueTime,
      estimatedDuration: taskForm.estimatedDuration,
      subtasks: taskForm.subtasks.filter(item => item.trim()),
      reminderEnabled: taskForm.reminderEnabled,
      reminderTime: taskForm.reminderTime,
      completed: false,
      progress: 0,
      createTime: new Date().toISOString(),
      status: 'pending'
    }

    // 保存任务（这里应该保存到本地存储或服务器）
    this.saveTask(taskData)
  },

  // 保存任务
  async saveTask(taskData) {
    wx.showLoading({
      title: '保存中...',
      mask: true
    })

    try {
      // 使用SmartApi保存到云数据库
      const result = await SmartApi.addTask(taskData)

      wx.hideLoading()

      if (result.success) {
        wx.showToast({
          title: '任务创建成功',
          icon: 'success'
        })

        // 返回上一页
        setTimeout(() => {
          wx.navigateBack()
        }, 1500)
      } else {
        wx.showToast({
          title: result.error || '保存失败',
          icon: 'none'
        })
      }

    } catch (error) {
      wx.hideLoading()
      console.error('保存任务失败:', error)
      wx.showToast({
        title: '保存失败，请重试',
        icon: 'none'
      })
    }
  }
})