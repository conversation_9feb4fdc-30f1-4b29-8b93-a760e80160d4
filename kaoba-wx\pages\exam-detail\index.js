// pages/exam-detail/index.js
const SmartApi = require('../../utils/smartApi')

Page({
  data: {
    examId: '',
    exam: {},
    countdown: {
      days: 0,
      hours: 0,
      minutes: 0,
      seconds: 0
    },
    relatedTasks: [],
    countdownTimer: null,

    // 搭子小组相关
    hasStudyGroup: false,
    studyGroups: []
  },

  onLoad(options) {
    const examId = options.id
    if (examId) {
      this.setData({ examId })
      this.loadExamDetail(examId)
      this.loadStudyGroups()
    }
  },

  onShow() {
    // 页面显示时刷新数据和启动倒计时
    if (this.data.examId) {
      this.loadExamDetail(this.data.examId)
      this.startCountdown()
    }
  },

  onHide() {
    // 页面隐藏时停止倒计时
    this.stopCountdown()
  },

  onUnload() {
    // 页面卸载时停止倒计时
    this.stopCountdown()
  },

  // 加载考试详情
  async loadExamDetail(examId) {
    wx.showLoading({
      title: '加载中...',
      mask: true
    })

    try {
      // 从数据库获取考试详情
      const examResult = await SmartApi.getExamById(examId)

      if (examResult.success && examResult.data) {
        const examData = examResult.data

        // 处理考试数据
        const exam = {
          id: examData._id || examData.id,
          name: examData.title || examData.name,
          type: examData.type || '考试',
          description: examData.description || '',
          date: examData.examDate || examData.date,
          time: examData.examTime || examData.time || '09:00',
          importance: examData.importance || 'medium',
          importanceText: this.getImportanceText(examData.importance),
          status: examData.status || 'active',
          statusText: this.getStatusText(examData.status),
          isActive: examData.status === 'active',
          targetScore: examData.targetScore || 0,
          location: examData.location || '',
          subject: examData.subject || '',
          // 计算进度相关数据
          overallProgress: 0,
          timeProgress: 0,
          remainingDays: 0,
          totalTasks: 0,
          completedTasks: 0,
          totalStudyTime: '0h',
          subjects: examData.subjects || []
        }

        // 计算剩余天数
        if (exam.date) {
          const examDate = new Date(exam.date)
          const today = new Date()
          const timeDiff = examDate.getTime() - today.getTime()
          exam.remainingDays = Math.ceil(timeDiff / (1000 * 3600 * 24))
        }

        // 获取相关任务
        await this.loadRelatedTasks(examId)

        this.setData({ exam })
        this.calculateCountdown()

        wx.hideLoading()
      } else {
        wx.hideLoading()
        wx.showToast({
          title: '考试不存在',
          icon: 'none'
        })
        setTimeout(() => {
          wx.navigateBack()
        }, 1500)
      }
    } catch (error) {
      wx.hideLoading()
      console.error('加载考试详情失败:', error)
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      })
    }
  },

  // 获取重要性文本
  getImportanceText(importance) {
    const importanceMap = {
      'high': '非常重要',
      'medium': '重要',
      'low': '一般'
    }
    return importanceMap[importance] || '一般'
  },

  // 获取状态文本
  getStatusText(status) {
    const statusMap = {
      'active': '当前考试',
      'upcoming': '即将开始',
      'completed': '已完成',
      'cancelled': '已取消'
    }
    return statusMap[status] || '未知状态'
  },

  // 加载相关任务
  async loadRelatedTasks(examId) {
    try {
      // 获取与该考试相关的任务
      const tasksResult = await SmartApi.getTasks({ examId }, 10, 0)

      if (tasksResult.success && tasksResult.data) {
        const relatedTasks = tasksResult.data.map(task => ({
          id: task._id || task.id,
          title: task.title,
          subject: task.subject || '未分类',
          dueDate: this.formatDate(task.dueDate),
          priority: task.priority || 'medium',
          priorityText: this.getPriorityText(task.priority),
          progress: task.progress || 0,
          completed: task.completed || false
        }))

        this.setData({ relatedTasks })

        // 更新考试的任务统计
        const totalTasks = relatedTasks.length
        const completedTasks = relatedTasks.filter(task => task.completed).length

        this.setData({
          'exam.totalTasks': totalTasks,
          'exam.completedTasks': completedTasks,
          'exam.overallProgress': totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0
        })
      }
    } catch (error) {
      console.error('加载相关任务失败:', error)
      this.setData({ relatedTasks: [] })
    }
  },

  // 格式化日期
  formatDate(dateStr) {
    if (!dateStr) return ''
    const date = new Date(dateStr)
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    return `${month}-${day}`
  },

  // 获取优先级文本
  getPriorityText(priority) {
    const priorityMap = {
      'high': '高',
      'medium': '中',
      'low': '低'
    }
    return priorityMap[priority] || '中'
  },

  // 计算倒计时
  calculateCountdown() {
    const examDate = new Date(`${this.data.exam.date} ${this.data.exam.time}`)
    const now = new Date()
    const diff = examDate.getTime() - now.getTime()

    if (diff > 0) {
      const days = Math.floor(diff / (1000 * 60 * 60 * 24))
      const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
      const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
      const seconds = Math.floor((diff % (1000 * 60)) / 1000)

      this.setData({
        countdown: {
          days: days.toString().padStart(2, '0'),
          hours: hours.toString().padStart(2, '0'),
          minutes: minutes.toString().padStart(2, '0'),
          seconds: seconds.toString().padStart(2, '0')
        }
      })
    }
  },

  // 启动倒计时
  startCountdown() {
    this.stopCountdown() // 先停止之前的计时器
    this.countdownTimer = setInterval(() => {
      this.calculateCountdown()
    }, 1000)
  },

  // 停止倒计时
  stopCountdown() {
    if (this.countdownTimer) {
      clearInterval(this.countdownTimer)
      this.countdownTimer = null
    }
  },

  // 设为当前考试
  setAsActiveExam() {
    wx.showModal({
      title: '设为当前考试',
      content: '确定要将此考试设为当前考试吗？',
      success: (res) => {
        if (res.confirm) {
          const exam = { ...this.data.exam }
          exam.isActive = true
          exam.status = 'active'
          exam.statusText = '当前考试'

          this.setData({ exam })

          wx.showToast({
            title: '设置成功',
            icon: 'success'
          })
        }
      }
    })
  },

  // 编辑考试
  editExam() {
    wx.navigateTo({
      url: `/pages/edit-exam/index?id=${this.data.examId}`
    })
  },

  // 显示更多操作
  showMoreActions() {
    wx.showActionSheet({
      itemList: ['复制考试', '删除考试', '导出数据'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            this.duplicateExam()
            break
          case 1:
            this.deleteExam()
            break
          case 2:
            this.exportExamData()
            break
        }
      }
    })
  },

  // 查看任务详情
  viewTaskDetail(e) {
    const taskId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/task-detail/index?id=${taskId}`
    })
  },

  // 查看所有任务
  viewAllTasks() {
    wx.switchTab({
      url: '/pages/task-center/index'
    })
  },

  // 添加任务
  addTask() {
    wx.navigateTo({
      url: `/pages/add-task/index?examId=${this.data.examId}`
    })
  },

  // 开始学习
  startStudy() {
    wx.switchTab({
      url: '/pages/pomodoro/index'
    })
  },

  // 设置提醒
  setReminder() {
    wx.showModal({
      title: '设置提醒',
      content: '是否开启考试提醒？',
      success: (res) => {
        if (res.confirm) {
          wx.showToast({
            title: '提醒设置成功',
            icon: 'success'
          })
        }
      }
    })
  },

  // 分享考试
  shareExam() {
    return {
      title: `我正在准备${this.data.exam.name}`,
      path: `/pages/exam-detail/index?id=${this.data.examId}`
    }
  },

  // 复制考试
  duplicateExam() {
    wx.navigateTo({
      url: `/pages/add-exam/index?duplicate=${this.data.examId}`
    })
  },

  // 删除考试
  deleteExam() {
    wx.showModal({
      title: '确认删除',
      content: '确定要删除此考试吗？相关任务也将被删除，此操作不可恢复。',
      confirmText: '删除',
      confirmColor: '#FF4D4F',
      success: (res) => {
        if (res.confirm) {
          wx.showToast({
            title: '考试已删除',
            icon: 'success'
          })

          setTimeout(() => {
            wx.navigateBack()
          }, 1500)
        }
      }
    })
  },

  // 导出考试数据
  exportExamData() {
    wx.showToast({
      title: '数据导出中...',
      icon: 'loading'
    })

    setTimeout(() => {
      wx.showToast({
        title: '导出成功',
        icon: 'success'
      })
    }, 2000)
  },

  // 分享功能
  onShareAppMessage() {
    return this.shareExam()
  },

  // 加载搭子小组
  async loadStudyGroups() {
    try {
      const result = await SmartApi.getMyStudyGroups()

      if (result.success && result.data) {
        // 筛选出与当前考试相关的小组
        const examGroups = result.data.filter(group => group.examId === this.data.examId)

        this.setData({
          studyGroups: examGroups,
          hasStudyGroup: examGroups.length > 0
        })
      }
    } catch (error) {
      console.error('加载搭子小组失败:', error)
    }
  },

  // 分享计划到搭子小组
  onShareToGroup() {
    const { hasStudyGroup, studyGroups, relatedTasks, exam } = this.data

    if (!hasStudyGroup) {
      wx.showModal({
        title: '提示',
        content: '您还没有加入该考试的搭子小组，是否创建一个？',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/create-study-group/index'
            })
          }
        }
      })
      return
    }

    if (relatedTasks.length === 0) {
      wx.showToast({
        title: '暂无复习计划可分享',
        icon: 'none'
      })
      return
    }

    // 构建计划数据
    const planData = {
      title: `${exam.title}复习计划`,
      description: exam.description || '',
      tasks: relatedTasks.map(task => ({
        title: task.title,
        description: task.description,
        subject: task.subject,
        priority: task.priority,
        estimatedDuration: task.estimatedDuration
      })),
      totalTasks: relatedTasks.length,
      estimatedDays: this.calculateEstimatedDays(relatedTasks)
    }

    // 如果只有一个小组，直接分享
    if (studyGroups.length === 1) {
      this.shareToGroup(studyGroups[0]._id, planData)
    } else {
      // 多个小组时让用户选择
      this.showGroupSelector(planData)
    }
  },

  // 分享到指定小组
  async shareToGroup(groupId, planData) {
    wx.showLoading({
      title: '分享中...',
      mask: true
    })

    try {
      const result = await SmartApi.shareGroupPlan(groupId, planData)

      wx.hideLoading()

      if (result.success) {
        wx.showToast({
          title: '分享成功！',
          icon: 'success'
        })
      } else {
        wx.showToast({
          title: result.error || '分享失败',
          icon: 'none'
        })
      }
    } catch (error) {
      wx.hideLoading()
      wx.showToast({
        title: '分享失败，请重试',
        icon: 'none'
      })
    }
  },

  // 显示小组选择器
  showGroupSelector(planData) {
    const { studyGroups } = this.data
    const groupNames = studyGroups.map(group => group.groupName)

    wx.showActionSheet({
      itemList: groupNames,
      success: (res) => {
        const selectedGroup = studyGroups[res.tapIndex]
        this.shareToGroup(selectedGroup._id, planData)
      }
    })
  },

  // 计算预估天数
  calculateEstimatedDays(tasks) {
    const totalHours = tasks.reduce((sum, task) => {
      return sum + (task.estimatedDuration || 2) // 默认2小时
    }, 0)

    // 假设每天学习4小时
    return Math.ceil(totalHours / 4)
  }
})