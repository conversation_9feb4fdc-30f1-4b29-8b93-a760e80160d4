// 数据库调试页面
const DatabaseManager = require('../../utils/databaseManager')

Page({
  data: {
    dbStatus: null,
    loading: false,
    logs: []
  },

  onLoad() {
    this.checkDatabaseStatus()
  },

  // 检查数据库状态
  async checkDatabaseStatus() {
    this.setData({ loading: true })
    this.addLog('开始检查数据库状态...')

    try {
      const status = await DatabaseManager.getSystemStatus()
      this.setData({ dbStatus: status })
      
      if (status.success) {
        this.addLog('数据库状态检查成功')
        this.addLog(`连接状态: ${status.data.connection.success ? '正常' : '失败'}`)
        
        if (status.data.database.success) {
          const stats = status.data.database.data
          this.addLog(`任务数量: ${stats.tasks}`)
          this.addLog(`考试数量: ${stats.exams}`)
          this.addLog(`学习会话: ${stats.studySessions}`)
          this.addLog(`番茄钟: ${stats.pomodoroSessions}`)
          this.addLog(`总计: ${stats.total} 条记录`)
        } else {
          this.addLog(`数据库错误: ${status.data.database.error}`)
        }
      } else {
        this.addLog(`检查失败: ${status.error}`)
      }
    } catch (error) {
      this.addLog(`检查异常: ${error.message}`)
    }

    this.setData({ loading: false })
  },

  // 重新初始化数据库
  async reinitDatabase() {
    this.setData({ loading: true })
    this.addLog('开始重新初始化数据库...')

    try {
      const result = await DatabaseManager.initDatabase()
      
      if (result.success) {
        this.addLog('数据库初始化成功')
        this.addLog(result.message)
        
        // 重新检查状态
        setTimeout(() => {
          this.checkDatabaseStatus()
        }, 2000)
      } else {
        this.addLog(`初始化失败: ${result.error}`)
      }
    } catch (error) {
      this.addLog(`初始化异常: ${error.message}`)
    }

    this.setData({ loading: false })
  },

  // 重置数据库
  async resetDatabase() {
    const that = this
    
    wx.showModal({
      title: '确认重置',
      content: '这将删除所有数据并重新创建示例数据，确定继续吗？',
      success: async (res) => {
        if (res.confirm) {
          that.setData({ loading: true })
          that.addLog('开始重置数据库...')

          try {
            const result = await DatabaseManager.resetDatabase()
            
            if (result.success) {
              that.addLog('数据库重置成功')
              that.addLog(result.message)
              
              // 重新检查状态
              setTimeout(() => {
                that.checkDatabaseStatus()
              }, 2000)
            } else {
              that.addLog(`重置失败: ${result.error}`)
            }
          } catch (error) {
            that.addLog(`重置异常: ${error.message}`)
          }

          that.setData({ loading: false })
        }
      }
    })
  },

  // 手动创建示例数据
  async createSampleData() {
    this.setData({ loading: true })
    this.addLog('开始创建示例数据...')

    try {
      // 直接调用云函数创建数据
      const result = await wx.cloud.callFunction({
        name: 'initDatabase',
        data: { action: 'init' }
      })

      if (result.result && result.result.success) {
        this.addLog('示例数据创建成功')
        this.addLog(result.result.message)

        // 重新检查状态
        setTimeout(() => {
          this.checkDatabaseStatus()
        }, 2000)
      } else {
        this.addLog(`创建失败: ${result.result?.error || '未知错误'}`)
      }
    } catch (error) {
      this.addLog(`创建异常: ${error.message}`)
    }

    this.setData({ loading: false })
  },

  // 强制初始化数据库
  async forceInitDatabase() {
    this.setData({ loading: true })
    this.addLog('开始强制初始化数据库...')

    try {
      const result = await wx.cloud.callFunction({
        name: 'initDatabase',
        data: { action: 'force' }
      })

      if (result.result && result.result.success) {
        this.addLog('强制初始化成功')
        this.addLog(result.result.message)

        // 重新检查状态
        setTimeout(() => {
          this.checkDatabaseStatus()
        }, 2000)
      } else {
        this.addLog(`强制初始化失败: ${result.result?.error || '未知错误'}`)
      }
    } catch (error) {
      this.addLog(`强制初始化异常: ${error.message}`)
    }

    this.setData({ loading: false })
  },

  // 查看集合列表
  async listCollections() {
    this.setData({ loading: true })
    this.addLog('开始查看集合列表...')

    try {
      const db = wx.cloud.database()
      
      // 尝试查询各个集合
      const collections = ['tasks', 'exams', 'study_sessions', 'pomodoro_sessions']
      
      for (const collectionName of collections) {
        try {
          const result = await db.collection(collectionName).limit(1).get()
          this.addLog(`${collectionName}: ${result.data.length > 0 ? '存在数据' : '集合存在但无数据'}`)
        } catch (error) {
          this.addLog(`${collectionName}: 集合不存在或查询失败`)
        }
      }
    } catch (error) {
      this.addLog(`查询异常: ${error.message}`)
    }

    this.setData({ loading: false })
  },

  // 添加日志
  addLog(message) {
    const logs = this.data.logs
    const timestamp = new Date().toLocaleTimeString()
    logs.unshift(`[${timestamp}] ${message}`)
    
    // 只保留最近50条日志
    if (logs.length > 50) {
      logs.splice(50)
    }
    
    this.setData({ logs })
  },

  // 清空日志
  clearLogs() {
    this.setData({ logs: [] })
  },

  // 复制日志
  copyLogs() {
    const logText = this.data.logs.join('\n')
    wx.setClipboardData({
      data: logText,
      success: () => {
        wx.showToast({
          title: '日志已复制',
          icon: 'success'
        })
      }
    })
  }
})
