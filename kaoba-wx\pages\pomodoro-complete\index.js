// pages/pomodoro-complete/index.js
const SmartApi = require('../../utils/smartApi')

Page({
  data: {
    sessionData: {
      duration: '25分钟',
      efficiency: 88,
      taskName: '数学高数第一章复习',
      streak: '15天',
      startTime: '14:30',
      endTime: '14:55',
      isNewRecord: false,
      rating: 0,
      ratingText: '请为这次专注打分',
      feedback: '',
      notes: ''
    },

    ratingStars: [
      { value: 1, filled: false },
      { value: 2, filled: false },
      { value: 3, filled: false },
      { value: 4, filled: false },
      { value: 5, filled: false }
    ],

    todayAchievements: [
      {
        id: 'daily_goal',
        icon: '🎯',
        name: '每日目标',
        progress: '4/5',
        unlocked: false
      },
      {
        id: 'focus_master',
        icon: '🧘',
        name: '专注大师',
        progress: '15/20',
        unlocked: false
      },
      {
        id: 'streak_keeper',
        icon: '🔥',
        name: '坚持达人',
        progress: '15天',
        unlocked: true
      }
    ],

    breakSuggestion: {
      icon: '☕',
      title: '短暂休息',
      description: '建议休息5-10分钟，活动一下身体',
      duration: '5分钟'
    },

    todayStats: {
      completedPomodoros: 4,
      totalTime: '2.5h',
      goalProgress: 83
    },

    selectedBreak: '',
    breakOptions: [
      { value: '5min', label: '5分钟休息' },
      { value: '10min', label: '10分钟休息' },
      { value: '15min', label: '15分钟休息' },
      { value: 'none', label: '不休息' }
    ],

    showBreakTimer: false,
    breakProgress: 0,
    breakTimeDisplay: '05:00',
    breakRunning: false,
    breakTimer: null
  },

  onLoad(options) {
    this.loadSessionData(options)
    this.initRating()
  },

  onUnload() {
    this.saveSessionData()
    this.clearBreakTimer()
  },

  // 加载会话数据
  loadSessionData(options) {
    // 从参数中获取番茄钟会话数据
    const duration = options.duration || '25'
    const taskId = options.taskId || ''
    const startTime = options.startTime || ''

    // 计算结束时间
    const now = new Date()
    const endTime = now.toTimeString().slice(0, 5)

    // 模拟效率计算（实际应该基于用户行为）
    const efficiency = Math.floor(Math.random() * 20) + 80 // 80-100%

    // 更新会话数据
    this.setData({
      'sessionData.duration': `${duration}分钟`,
      'sessionData.startTime': startTime,
      'sessionData.endTime': endTime,
      'sessionData.efficiency': efficiency
    })

    // 检查是否创造新纪录
    this.checkNewRecord()
  },

  // 初始化评分
  initRating() {
    const stars = this.data.ratingStars.map(star => ({
      ...star,
      filled: false
    }))

    this.setData({ ratingStars: stars })
  },

  // 检查新纪录
  checkNewRecord() {
    // 模拟检查逻辑
    const isNewRecord = Math.random() > 0.8 // 20%概率创造新纪录

    if (isNewRecord) {
      this.setData({
        'sessionData.isNewRecord': true
      })

      wx.showToast({
        title: '🏆 创造新纪录！',
        icon: 'none',
        duration: 2000
      })
    }
  },

  // 评分
  rateSession(e) {
    const rating = e.currentTarget.dataset.rating
    const stars = this.data.ratingStars.map((star, index) => ({
      ...star,
      filled: index < rating
    }))

    const ratingTexts = [
      '',
      '需要改进',
      '一般般',
      '还不错',
      '很棒',
      '完美！'
    ]

    this.setData({
      ratingStars: stars,
      'sessionData.rating': rating,
      'sessionData.ratingText': ratingTexts[rating]
    })
  },

  // 更新笔记
  updateNotes(e) {
    this.setData({
      'sessionData.notes': e.detail.value
    })
  },

  // 继续学习
  continueStudy() {
    wx.switchTab({
      url: '/pages/pomodoro/index'
    })
  },

  // 休息一下
  takeBreak() {
    this.setData({ showBreakTimer: true })
    this.startBreakTimer(5) // 默认5分钟休息
  },

  // 查看任务
  viewTasks() {
    wx.switchTab({
      url: '/pages/task-center/index'
    })
  },

  // 查看统计
  viewStats() {
    wx.switchTab({
      url: '/pages/data-center/index'
    })
  },

  // 开始休息计时器
  startBreakTimer(minutes) {
    this.clearBreakTimer()

    let totalSeconds = minutes * 60
    let currentSeconds = totalSeconds

    this.setData({
      breakRunning: true,
      breakProgress: 0,
      breakTimeDisplay: this.formatTime(currentSeconds)
    })

    this.data.breakTimer = setInterval(() => {
      currentSeconds--

      const progress = ((totalSeconds - currentSeconds) / totalSeconds) * 100

      this.setData({
        breakProgress: progress,
        breakTimeDisplay: this.formatTime(currentSeconds)
      })

      if (currentSeconds <= 0) {
        this.completeBreak()
      }
    }, 1000)
  },

  // 暂停休息
  pauseBreak() {
    this.clearBreakTimer()
    this.setData({ breakRunning: false })
  },

  // 继续休息
  resumeBreak() {
    // 重新开始计时器（简化实现）
    const timeStr = this.data.breakTimeDisplay
    const [minutes, seconds] = timeStr.split(':').map(Number)
    const totalSeconds = minutes * 60 + seconds

    this.startBreakTimer(totalSeconds / 60)
  },

  // 跳过休息
  skipBreak() {
    this.clearBreakTimer()
    this.hideBreakTimer()
  },

  // 完成休息
  completeBreak() {
    this.clearBreakTimer()
    this.setData({ breakRunning: false })

    wx.showToast({
      title: '休息完成！',
      icon: 'success'
    })

    setTimeout(() => {
      this.hideBreakTimer()
    }, 1500)
  },

  // 隐藏休息计时器
  hideBreakTimer() {
    this.setData({ showBreakTimer: false })
  },

  // 清除休息计时器
  clearBreakTimer() {
    if (this.data.breakTimer) {
      clearInterval(this.data.breakTimer)
      this.data.breakTimer = null
    }
  },

  // 格式化时间
  formatTime(seconds) {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  },

  // 分享会话
  shareSession() {
    const { sessionData } = this.data

    return {
      title: `我刚完成了${sessionData.duration}的专注学习！`,
      path: '/pages/pomodoro-complete/index',
      imageUrl: '/images/share-pomodoro.png' // 需要添加分享图片
    }
  },

  // 完成会话
  finishSession() {
    this.saveSessionData()

    wx.showToast({
      title: '学习记录已保存',
      icon: 'success'
    })

    setTimeout(() => {
      wx.switchTab({
        url: '/pages/home/<USER>'
      })
    }, 1500)
  },

  // 保存会话数据
  async saveSessionData() {
    const { sessionData } = this.data

    try {
      // 构建番茄钟会话数据
      const pomodoroData = {
        subject: sessionData.taskName || '自由专注',
        taskId: sessionData.taskId || null,
        duration: parseInt(sessionData.duration) || 25,
        completed: true,
        startTime: new Date(sessionData.startTime || new Date()),
        endTime: new Date(sessionData.endTime || new Date()),
        efficiency: sessionData.efficiency || 85,
        rating: sessionData.rating || 0,
        notes: sessionData.notes || ''
      }

      // 使用SmartApi保存到云数据库
      const result = await SmartApi.addPomodoroSession(pomodoroData)

      if (result.success) {
        console.log('番茄钟会话保存成功:', result.data)

        // 更新今日统计
        this.updateTodayStats({
          id: 'session_' + Date.now(),
          type: 'pomodoro',
          taskName: sessionData.taskName,
          duration: sessionData.duration,
          efficiency: sessionData.efficiency,
          rating: sessionData.rating,
          notes: sessionData.notes,
          startTime: sessionData.startTime,
          endTime: sessionData.endTime,
          date: new Date().toISOString().split('T')[0],
          timestamp: new Date().toISOString()
        })
      } else {
        console.error('保存番茄钟会话失败:', result.error)
        wx.showToast({
          title: '保存失败，请重试',
          icon: 'none'
        })
      }

    } catch (error) {
      console.error('保存学习记录失败:', error)
      wx.showToast({
        title: '保存失败，请重试',
        icon: 'none'
      })
    }
  },

  // 更新今日统计
  updateTodayStats(newRecord) {
    try {
      const today = new Date().toISOString().split('T')[0]
      const todayStats = wx.getStorageSync('todayStats') || {
        date: today,
        completedPomodoros: 0,
        totalMinutes: 0,
        goalProgress: 0
      }

      // 如果是新的一天，重置统计
      if (todayStats.date !== today) {
        todayStats.date = today
        todayStats.completedPomodoros = 0
        todayStats.totalMinutes = 0
        todayStats.goalProgress = 0
      }

      // 更新统计
      todayStats.completedPomodoros += 1
      todayStats.totalMinutes += parseInt(newRecord.duration)

      // 计算目标进度（假设每日目标是5个番茄钟）
      const dailyGoal = 5
      todayStats.goalProgress = Math.min(100, (todayStats.completedPomodoros / dailyGoal) * 100)

      // 保存统计
      wx.setStorageSync('todayStats', todayStats)

      // 更新页面显示
      this.setData({
        'todayStats.completedPomodoros': todayStats.completedPomodoros,
        'todayStats.totalTime': `${(todayStats.totalMinutes / 60).toFixed(1)}h`,
        'todayStats.goalProgress': Math.round(todayStats.goalProgress)
      })

    } catch (error) {
      console.error('更新今日统计失败:', error)
    }
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止事件冒泡
  },

  // 分享功能
  onShareAppMessage() {
    return this.shareSession()
  }
})