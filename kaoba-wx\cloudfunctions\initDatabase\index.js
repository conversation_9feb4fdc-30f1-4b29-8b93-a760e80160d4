// 数据库初始化云函数
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const { action = 'init', force = false } = event

  try {
    switch (action) {
      case 'init':
        return await initDatabase(force)
      case 'check':
        return await checkDatabase()
      case 'reset':
        return await resetDatabase()
      case 'force':
        return await initDatabase(true)
      default:
        return { success: false, error: '未知操作' }
    }
  } catch (error) {
    console.error('数据库初始化云函数错误:', error)
    return { success: false, error: error.message }
  }
}

// 初始化数据库
async function initDatabase(force = false) {
  console.log('开始初始化数据库...', force ? '(强制模式)' : '')

  try {
    // 检查是否已有数据（除非强制初始化）
    if (!force) {
      const hasData = await checkExistingData()

      if (hasData) {
        console.log('数据库已有数据，跳过初始化')
        return {
          success: true,
          message: '数据库已有数据，跳过初始化',
          hasData: true
        }
      }
    } else {
      console.log('强制初始化模式，将创建缺失的数据')
    }

    console.log('数据库初始化完成')
    return {
      success: true,
      message: '数据库初始化完成',
      data: {
        collections: collectionsCreated
      }
    }
  } catch (error) {
    console.error('数据库初始化失败:', error)
    return { success: false, error: error.message }
  }
}

// 检查现有数据
async function checkExistingData() {
  try {
    // 检查关键集合是否都存在且有数据
    const [usersResult, tasksResult] = await Promise.all([
      db.collection('users').limit(1).get(),
      db.collection('tasks').limit(1).get()
    ])

    // 只有当用户集合和任务集合都有数据时才跳过初始化
    const hasUsers = usersResult.data.length > 0
    const hasTasks = tasksResult.data.length > 0

    console.log(`检查数据: users=${hasUsers}, tasks=${hasTasks}`)

    return hasUsers && hasTasks
  } catch (error) {
    // 如果集合不存在，返回false
    console.log('关键集合不存在或查询失败，需要初始化:', error.message)
    return false
  }
}

// 检查数据库状态
async function checkDatabase() {
  try {
    const stats = await Promise.all([
      db.collection('users').count(),
      db.collection('tasks').count(),
      db.collection('exams').count(),
      db.collection('study_sessions').count(),
      db.collection('pomodoro_sessions').count()
    ])

    return {
      success: true,
      data: {
        users: stats[0].total,
        tasks: stats[1].total,
        exams: stats[2].total,
        studySessions: stats[3].total,
        pomodoroSessions: stats[4].total,
        total: stats.reduce((sum, stat) => sum + stat.total, 0)
      }
    }
  } catch (error) {
    return { success: false, error: error.message }
  }
}

// 以下函数已删除，不再创建示例数据
/*
// 创建示例用户
async function createSampleUsers() {
  const sampleUsers = [
    {
      openid: 'sample_user_001',
      appid: 'sample_app',
      unionid: null,
      nickName: '学霸小明',
      avatarUrl: '',
      gender: 1,
      country: '中国',
      province: '北京',
      city: '北京',
      language: 'zh_CN',
      isFirstLogin: false,
      loginCount: 15,
      lastLoginTime: new Date(),
      createTime: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30天前
      updateTime: new Date()
    },
    {
      openid: 'sample_user_002',
      appid: 'sample_app',
      unionid: null,
      nickName: '考试达人',
      avatarUrl: '',
      gender: 2,
      country: '中国',
      province: '上海',
      city: '上海',
      language: 'zh_CN',
      isFirstLogin: false,
      loginCount: 8,
      lastLoginTime: new Date(),
      createTime: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000), // 15天前
      updateTime: new Date()
    }
  ]

  const results = []
  for (const user of sampleUsers) {
    try {
      const result = await db.collection('users').add({ data: user })
      results.push(result._id)
      console.log('创建示例用户:', user.nickName)
    } catch (error) {
      console.log('用户创建失败:', user.nickName, error.message)
    }
  }

  return { count: results.length, ids: results }
}

// 创建示例任务
async function createSampleTasks() {
  const sampleTasks = [
    {
      title: '数学高数第一章复习',
      subject: '数学',
      priority: 'high',
      estimatedTime: 120,
      dueDate: new Date(),
      description: '复习极限与连续性相关内容',
      completed: false,
      createTime: new Date(),
      updateTime: new Date()
    },
    {
      title: '英语单词背诵',
      subject: '英语',
      priority: 'medium',
      estimatedTime: 30,
      dueDate: new Date(),
      description: '背诵考研核心词汇100个',
      completed: true,
      createTime: new Date(),
      updateTime: new Date(),
      completedTime: new Date()
    },
    {
      title: '政治马原理论学习',
      subject: '政治',
      priority: 'low',
      estimatedTime: 60,
      dueDate: new Date(),
      description: '学习马克思主义基本原理',
      completed: false,
      createTime: new Date(),
      updateTime: new Date()
    }
  ]

  const results = []
  for (const task of sampleTasks) {
    try {
      const result = await db.collection('tasks').add({ data: task })
      results.push(result._id)
      console.log('创建示例任务:', task.title)
    } catch (error) {
      console.log('任务创建失败:', task.title, error.message)
    }
  }

  return { count: results.length, ids: results }
}

// 创建示例考试
async function createSampleExams() {
  const futureDate = new Date()
  futureDate.setDate(futureDate.getDate() + 30)

  const sampleExams = [
    {
      title: '高等数学期末考试',
      subject: '数学',
      examDate: futureDate,
      examTime: '09:00',
      location: '教学楼A101',
      description: '涵盖微积分、线性代数等内容',
      reminderDays: 3,
      createTime: new Date(),
      updateTime: new Date()
    },
    {
      title: '大学英语四级',
      subject: '英语',
      examDate: new Date(futureDate.getTime() + 7 * 24 * 60 * 60 * 1000),
      examTime: '09:00',
      location: '教学楼B201',
      description: '英语四级考试',
      reminderDays: 7,
      createTime: new Date(),
      updateTime: new Date()
    }
  ]

  const results = []
  for (const exam of sampleExams) {
    try {
      const result = await db.collection('exams').add({ data: exam })
      results.push(result._id)
      console.log('创建示例考试:', exam.title)
    } catch (error) {
      console.log('考试创建失败:', exam.title, error.message)
    }
  }

  return { count: results.length, ids: results }
}

// 创建示例学习会话
async function createSampleStudySessions() {
  const today = new Date()
  const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000)

  const sampleSessions = [
    {
      subject: '数学',
      taskId: null,
      startTime: yesterday,
      endTime: new Date(yesterday.getTime() + 90 * 60 * 1000), // 90分钟
      totalDuration: 90,
      status: 'completed',
      notes: '复习了极限的概念和性质',
      createTime: yesterday
    },
    {
      subject: '英语',
      taskId: null,
      startTime: new Date(today.getTime() - 2 * 60 * 60 * 1000), // 2小时前
      endTime: new Date(today.getTime() - 90 * 60 * 1000), // 90分钟前
      totalDuration: 30,
      status: 'completed',
      notes: '背诵了30个单词',
      createTime: new Date(today.getTime() - 2 * 60 * 60 * 1000)
    }
  ]

  const results = []
  for (const session of sampleSessions) {
    try {
      const result = await db.collection('study_sessions').add({ data: session })
      results.push(result._id)
      console.log('创建示例学习会话:', session.subject)
    } catch (error) {
      console.log('学习会话创建失败:', session.subject, error.message)
    }
  }

  return { count: results.length, ids: results }
}

// 创建示例番茄钟会话
async function createSamplePomodoroSessions() {
  const today = new Date()

  const samplePomodoros = [
    {
      subject: '数学',
      taskId: null,
      duration: 25,
      completed: true,
      startTime: new Date(today.getTime() - 3 * 60 * 60 * 1000),
      endTime: new Date(today.getTime() - 3 * 60 * 60 * 1000 + 25 * 60 * 1000),
      createTime: new Date(today.getTime() - 3 * 60 * 60 * 1000)
    },
    {
      subject: '英语',
      taskId: null,
      duration: 25,
      completed: true,
      startTime: new Date(today.getTime() - 2 * 60 * 60 * 1000),
      endTime: new Date(today.getTime() - 2 * 60 * 60 * 1000 + 25 * 60 * 1000),
      createTime: new Date(today.getTime() - 2 * 60 * 60 * 1000)
    }
  ]

  const results = []
  for (const pomodoro of samplePomodoros) {
    try {
      const result = await db.collection('pomodoro_sessions').add({ data: pomodoro })
      results.push(result._id)
      console.log('创建示例番茄钟会话:', pomodoro.subject)
    } catch (error) {
      console.log('番茄钟会话创建失败:', pomodoro.subject, error.message)
    }
  }

  return { count: results.length, ids: results }
}

// 重置数据库（清空所有数据）
async function resetDatabase() {
  try {
    console.log('开始重置数据库...')

    // 删除所有集合的数据
    const deleteResults = await Promise.all([
      db.collection('users').where({}).remove(),
      db.collection('tasks').where({}).remove(),
      db.collection('exams').where({}).remove(),
      db.collection('study_sessions').where({}).remove(),
      db.collection('pomodoro_sessions').where({}).remove()
    ])

    const totalDeleted = deleteResults.reduce((sum, result) => sum + result.stats.removed, 0)
    console.log('数据库重置完成，删除记录数:', totalDeleted)

    // 重新初始化
    const initResult = await initDatabase()

    return {
      success: true,
      message: '数据库重置并重新初始化完成',
      deleted: totalDeleted,
      initResult
    }
  } catch (error) {
    console.error('数据库重置失败:', error)
    return { success: false, error: error.message }
  }
}
*/
