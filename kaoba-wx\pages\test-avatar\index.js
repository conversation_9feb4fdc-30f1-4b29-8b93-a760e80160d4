// pages/test-avatar/index.js
const SmartApi = require('../../utils/smartApi')

Page({
  data: {
    avatarUrl: '',
    uploading: false,
    errorLog: []
  },

  onLoad() {
    console.log('测试头像上传页面加载')
  },

  // 测试选择图片
  async testChooseImage() {
    console.log('开始测试选择图片')
    
    try {
      const res = await new Promise((resolve, reject) => {
        wx.chooseImage({
          count: 1,
          sizeType: ['compressed'],
          sourceType: ['album', 'camera'],
          success: resolve,
          fail: reject
        })
      })
      
      console.log('选择图片成功:', res)
      const filePath = res.tempFilePaths[0]
      
      wx.showToast({
        title: '图片选择成功',
        icon: 'success'
      })
      
      // 直接测试上传
      this.testUploadAvatar(filePath)
      
    } catch (error) {
      console.error('选择图片失败:', error)
      wx.showToast({
        title: '选择图片失败',
        icon: 'none'
      })
    }
  },

  // 测试上传头像
  async testUploadAvatar(filePath) {
    console.log('开始测试上传头像:', filePath)

    this.setData({ uploading: true })

    let loadingShown = false

    try {
      wx.showLoading({
        title: '上传中...',
        mask: true
      })
      loadingShown = true

      const result = await SmartApi.uploadAvatar(filePath)
      console.log('上传结果:', result)

      // 隐藏加载提示
      if (loadingShown) {
        wx.hideLoading()
        loadingShown = false
      }

      if (result.success) {
        this.setData({
          avatarUrl: result.data.avatarUrl
        })

        wx.showToast({
          title: '上传成功',
          icon: 'success'
        })
      } else {
        wx.showToast({
          title: result.error || '上传失败',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('上传异常:', error)

      // 确保隐藏加载提示
      if (loadingShown) {
        wx.hideLoading()
        loadingShown = false
      }

      // 记录错误日志
      const errorLog = this.data.errorLog
      errorLog.unshift({
        time: new Date().toLocaleTimeString(),
        error: error.message || '未知错误',
        stack: error.stack || ''
      })

      this.setData({ errorLog: errorLog.slice(0, 10) }) // 只保留最近10条错误

      wx.showModal({
        title: '上传异常',
        content: error.message || '未知错误',
        showCancel: false
      })
    } finally {
      // 最终确保隐藏加载提示
      if (loadingShown) {
        wx.hideLoading()
      }
      this.setData({ uploading: false })
    }
  },

  // 测试云函数调用
  async testCloudFunction() {
    console.log('测试云函数调用')
    
    try {
      const result = await wx.cloud.callFunction({
        name: 'uploadAvatar',
        data: {
          action: 'getUploadUrl',
          data: {
            fileName: 'test.jpg',
            fileType: 'image'
          }
        }
      })
      
      console.log('云函数调用结果:', result)
      
      wx.showModal({
        title: '云函数测试',
        content: JSON.stringify(result.result, null, 2),
        showCancel: false
      })
      
    } catch (error) {
      console.error('云函数调用失败:', error)
      wx.showModal({
        title: '云函数测试失败',
        content: error.message,
        showCancel: false
      })
    }
  },

  // 测试用户信息
  async testUserInfo() {
    console.log('测试用户信息')
    
    const app = getApp()
    console.log('全局用户信息:', app.globalData.userInfo)
    console.log('全局openid:', app.globalData.openid)
    
    wx.showModal({
      title: '用户信息',
      content: `openid: ${app.globalData.openid}\nnickName: ${app.globalData.userInfo?.nickName}`,
      showCancel: false
    })
  },

  // 头像变更回调
  onAvatarChange(e) {
    console.log('头像变更回调:', e.detail)
    const { avatarUrl, success } = e.detail

    if (success) {
      this.setData({ avatarUrl })
      wx.showToast({
        title: '头像更新成功',
        icon: 'success'
      })
    }
  },

  // 简化测试：只测试云函数调用
  async testSimpleCloudFunction() {
    console.log('测试简化云函数调用')

    try {
      // 只测试getUploadUrl
      const result = await new Promise((resolve, reject) => {
        wx.cloud.callFunction({
          name: 'uploadAvatar',
          data: {
            action: 'getUploadUrl',
            data: {
              fileName: 'test.jpg',
              fileType: 'image'
            }
          },
          success: resolve,
          fail: reject
        })
      })

      console.log('简化测试结果:', result)

      // 安全地显示结果
      const safeResult = JSON.stringify(result, (key, value) => {
        if (typeof value === 'symbol') {
          return `[Symbol: ${value.toString()}]`
        }
        return value
      })

      wx.showModal({
        title: '简化测试结果',
        content: safeResult.substring(0, 500), // 限制长度
        showCancel: false
      })

    } catch (error) {
      console.error('简化测试失败:', error)
      wx.showModal({
        title: '简化测试失败',
        content: String(error.message || error),
        showCancel: false
      })
    }
  }
})
