/* pages/study-group-detail/index.wxss */
.container {
  background: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 小组信息头部 */
.group-header {
  background: white;
  padding: 32rpx 24rpx;
  margin-bottom: 16rpx;
}

.group-info {
  margin-bottom: 24rpx;
}

.group-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.exam-name {
  font-size: 28rpx;
  color: #666;
  display: block;
  margin-bottom: 8rpx;
}

.member-count {
  font-size: 24rpx;
  color: #999;
  background: #f0f0f0;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  display: inline-block;
}

.group-actions {
  display: flex;
  gap: 16rpx;
}

.group-actions .btn {
  flex: 1;
}

/* Tab导航 */
.tab-nav {
  background: white;
  display: flex;
  border-bottom: 1rpx solid #eee;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 24rpx 16rpx;
  font-size: 28rpx;
  color: #666;
  border-bottom: 4rpx solid transparent;
  transition: all 0.3s ease;
}

.tab-item.active {
  color: #1890ff;
  border-bottom-color: #1890ff;
  font-weight: 500;
}

/* Tab内容 */
.tab-content {
  background: white;
  margin-top: 16rpx;
  min-height: 400rpx;
}

/* 成员列表 */
.member-list {
  padding: 24rpx;
}

.member-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.member-item:last-child {
  border-bottom: none;
}

.member-avatar {
  margin-right: 20rpx;
}

.avatar-img {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: #f0f0f0;
}

.member-info {
  flex: 1;
  margin-right: 20rpx;
}

.member-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.member-stats {
  font-size: 24rpx;
  color: #666;
}

.progress-info {
  width: 120rpx;
  text-align: center;
}

.progress-bar {
  width: 100%;
  height: 8rpx;
  background: #f0f0f0;
  border-radius: 4rpx;
  overflow: hidden;
  margin-bottom: 8rpx;
}

.progress-fill {
  height: 100%;
  background: #1890ff;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 24rpx;
  color: #666;
}

/* 共享计划列表 */
.share-list {
  padding: 24rpx;
}

.share-item {
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
}

.share-item:last-child {
  margin-bottom: 0;
}

.share-header {
  margin-bottom: 20rpx;
}

.sharer-info {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.sharer-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: #f0f0f0;
}

.sharer-details {
  flex: 1;
}

.sharer-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  display: block;
  margin-bottom: 4rpx;
}

.share-time {
  font-size: 22rpx;
  color: #999;
}

.plan-content {
  margin-bottom: 20rpx;
}

.plan-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  display: block;
  margin-bottom: 12rpx;
}

.plan-desc {
  font-size: 26rpx;
  color: #666;
  display: block;
  margin-bottom: 16rpx;
  line-height: 1.5;
}

.plan-stats {
  display: flex;
  gap: 24rpx;
}

.stat-item {
  font-size: 24rpx;
  color: #999;
  background: white;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
}

.share-actions {
  display: flex;
  gap: 16rpx;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  background: white;
  border: 1rpx solid #e0e0e0;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #666;
}

.action-btn.liked {
  background: #e3f2fd;
  border-color: #1890ff;
  color: #1890ff;
}

.action-icon {
  font-size: 24rpx;
}

.action-text {
  font-size: 24rpx;
}

/* 小组动态列表 */
.activity-list {
  padding: 24rpx;
}

.activity-item {
  display: flex;
  gap: 16rpx;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-avatar .avatar-img {
  width: 60rpx;
  height: 60rpx;
}

.activity-content {
  flex: 1;
}

.activity-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  display: block;
  margin-bottom: 8rpx;
}

.user-name {
  font-weight: 500;
  color: #1890ff;
}

.activity-time {
  font-size: 22rpx;
  color: #999;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80rpx 40rpx;
}

.empty-icon {
  font-size: 80rpx;
  display: block;
  margin-bottom: 16rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  display: block;
  margin-bottom: 32rpx;
}

/* 底部操作 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 24rpx;
  background: white;
  border-top: 1rpx solid #eee;
}

/* 加载状态 */
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: #f5f5f5;
}

.loading-content {
  text-align: center;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 按钮样式 */
.btn {
  border: none;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  text-align: center;
  padding: 20rpx 32rpx;
  transition: all 0.3s ease;
}

.btn-primary {
  background: #1890ff;
  color: white;
}

.btn-secondary {
  background: #f0f0f0;
  color: #666;
}

.btn-danger {
  background: #ff4d4f;
  color: white;
  width: 100%;
}
