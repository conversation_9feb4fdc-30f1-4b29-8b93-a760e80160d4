// pages/edit-exam/index.js
const SmartApi = require('../../utils/smartApi')

Page({
  data: {
    examId: '',
    examForm: {
      name: '',
      type: 'university',
      description: '',
      date: '',
      startTime: '',
      endTime: '',
      subjects: [],
      targetScore: '',
      importance: 'medium',
      isActive: false,
      status: 'preparing',
      reminderEnabled: true,
      reminderFrequency: 'daily'
    },

    typeOptions: [
      { value: 'university', label: '大学考试', icon: '🎓' },
      { value: 'certification', label: '资格认证', icon: '📜' },
      { value: 'competition', label: '竞赛考试', icon: '🏆' },
      { value: 'entrance', label: '入学考试', icon: '🏫' },
      { value: 'professional', label: '职业考试', icon: '💼' },
      { value: 'language', label: '语言考试', icon: '🌍' }
    ],

    importanceOptions: [
      { value: 'high', label: '非常重要', icon: '🔴', color: '#FF4D4F' },
      { value: 'medium', label: '比较重要', icon: '🟡', color: '#FA8C16' },
      { value: 'low', label: '一般重要', icon: '🟢', color: '#52C41A' }
    ],

    statusOptions: [
      { value: 'preparing', label: '备考中', icon: '📚' },
      { value: 'ready', label: '准备就绪', icon: '✅' },
      { value: 'completed', label: '已完成', icon: '🎉' },
      { value: 'cancelled', label: '已取消', icon: '❌' }
    ],

    frequencyOptions: [
      { value: 'daily', label: '每日提醒' },
      { value: 'weekly', label: '每周提醒' },
      { value: 'custom', label: '自定义' }
    ],

    originalExam: {} // 保存原始考试数据
  },

  onLoad(options) {
    const examId = options.id
    if (examId) {
      this.setData({ examId })
      this.loadExamData(examId)
    }
  },

  // 加载考试数据
  loadExamData(examId) {
    // 模拟数据，实际应该从存储中获取
    const exam = {
      id: examId,
      name: '2025年考研',
      type: 'entrance',
      description: '全国硕士研究生统一招生考试，包含数学、英语、政治、专业课四门科目',
      date: '2025-12-23',
      startTime: '08:30',
      endTime: '17:00',
      targetScore: 380,
      importance: 'high',
      isActive: true,
      status: 'preparing',
      reminderEnabled: true,
      reminderFrequency: 'daily',
      subjects: [
        { name: '数学一', totalScore: 150, targetScore: 120 },
        { name: '英语一', totalScore: 100, targetScore: 70 },
        { name: '思想政治理论', totalScore: 100, targetScore: 70 },
        { name: '计算机专业基础', totalScore: 150, targetScore: 120 }
      ]
    }

    // 保存原始数据用于比较
    this.setData({
      originalExam: JSON.parse(JSON.stringify(exam)),
      examForm: {
        name: exam.name,
        type: exam.type,
        description: exam.description,
        date: exam.date,
        startTime: exam.startTime,
        endTime: exam.endTime,
        targetScore: exam.targetScore.toString(),
        importance: exam.importance,
        isActive: exam.isActive,
        status: exam.status,
        reminderEnabled: exam.reminderEnabled,
        reminderFrequency: exam.reminderFrequency,
        subjects: exam.subjects.map(item => ({ ...item, totalScore: item.totalScore.toString(), targetScore: item.targetScore.toString() }))
      }
    })
  },

  // 更新考试名称
  updateName(e) {
    this.setData({
      'examForm.name': e.detail.value
    })
  },

  // 更新考试描述
  updateDescription(e) {
    this.setData({
      'examForm.description': e.detail.value
    })
  },

  // 选择考试类型
  selectType(e) {
    const type = e.currentTarget.dataset.type
    this.setData({
      'examForm.type': type
    })
  },

  // 选择考试日期
  selectDate(e) {
    this.setData({
      'examForm.date': e.detail.value
    })
  },

  // 选择开始时间
  selectStartTime(e) {
    this.setData({
      'examForm.startTime': e.detail.value
    })
  },

  // 选择结束时间
  selectEndTime(e) {
    this.setData({
      'examForm.endTime': e.detail.value
    })
  },

  // 更新目标分数
  updateTargetScore(e) {
    this.setData({
      'examForm.targetScore': e.detail.value
    })
  },

  // 选择重要程度
  selectImportance(e) {
    const importance = e.currentTarget.dataset.importance
    this.setData({
      'examForm.importance': importance
    })
  },

  // 切换当前考试状态
  toggleActive(e) {
    this.setData({
      'examForm.isActive': e.detail.value
    })
  },

  // 选择考试状态
  selectStatus(e) {
    const status = e.currentTarget.dataset.status
    this.setData({
      'examForm.status': status
    })
  },

  // 切换提醒开关
  toggleReminder(e) {
    this.setData({
      'examForm.reminderEnabled': e.detail.value
    })
  },

  // 选择提醒频率
  selectFrequency(e) {
    const frequency = e.currentTarget.dataset.frequency
    this.setData({
      'examForm.reminderFrequency': frequency
    })
  },

  // 添加科目
  addSubject() {
    const subjects = [...this.data.examForm.subjects, { name: '', totalScore: '', targetScore: '' }]
    this.setData({
      'examForm.subjects': subjects
    })
  },

  // 更新科目名称
  updateSubjectName(e) {
    const index = e.currentTarget.dataset.index
    const value = e.detail.value
    const subjects = [...this.data.examForm.subjects]
    subjects[index].name = value

    this.setData({
      'examForm.subjects': subjects
    })
  },

  // 更新科目总分
  updateSubjectScore(e) {
    const index = e.currentTarget.dataset.index
    const value = e.detail.value
    const subjects = [...this.data.examForm.subjects]
    subjects[index].totalScore = value

    this.setData({
      'examForm.subjects': subjects
    })
  },

  // 更新科目目标分
  updateSubjectTarget(e) {
    const index = e.currentTarget.dataset.index
    const value = e.detail.value
    const subjects = [...this.data.examForm.subjects]
    subjects[index].targetScore = value

    this.setData({
      'examForm.subjects': subjects
    })
  },

  // 删除科目
  removeSubject(e) {
    const index = e.currentTarget.dataset.index
    const subjects = [...this.data.examForm.subjects]
    subjects.splice(index, 1)

    this.setData({
      'examForm.subjects': subjects
    })
  },

  // 取消编辑
  cancelEdit() {
    // 检查是否有未保存的更改
    if (this.hasUnsavedChanges()) {
      wx.showModal({
        title: '确认取消',
        content: '您有未保存的更改，确定要取消编辑吗？',
        success: (res) => {
          if (res.confirm) {
            wx.navigateBack()
          }
        }
      })
    } else {
      wx.navigateBack()
    }
  },

  // 检查是否有未保存的更改
  hasUnsavedChanges() {
    const current = this.data.examForm
    const original = this.data.originalExam

    return (
      current.name !== original.name ||
      current.type !== original.type ||
      current.description !== original.description ||
      current.date !== original.date ||
      current.startTime !== original.startTime ||
      current.endTime !== original.endTime ||
      current.targetScore !== original.targetScore.toString() ||
      current.importance !== original.importance ||
      current.isActive !== original.isActive ||
      current.status !== original.status ||
      current.reminderEnabled !== original.reminderEnabled ||
      current.reminderFrequency !== original.reminderFrequency ||
      JSON.stringify(current.subjects) !== JSON.stringify(original.subjects.map(item => ({ ...item, totalScore: item.totalScore.toString(), targetScore: item.targetScore.toString() })))
    )
  },

  // 删除考试
  deleteExam() {
    wx.showModal({
      title: '确认删除',
      content: '确定要删除此考试吗？相关任务也将被删除，此操作不可恢复。',
      confirmText: '删除',
      confirmColor: '#FF4D4F',
      success: (res) => {
        if (res.confirm) {
          // 这里应该从存储中删除考试
          wx.showToast({
            title: '考试已删除',
            icon: 'success'
          })

          setTimeout(() => {
            wx.navigateBack()
          }, 1500)
        }
      }
    })
  },

  // 更新考试
  updateExam() {
    const { examForm } = this.data

    // 验证必填字段
    if (!examForm.name || !examForm.name.trim()) {
      wx.showToast({
        title: '请输入考试名称',
        icon: 'none'
      })
      return
    }

    if (!examForm.date) {
      wx.showToast({
        title: '请选择考试日期',
        icon: 'none'
      })
      return
    }

    // 验证科目信息
    const validSubjects = examForm.subjects.filter(subject =>
      subject.name.trim() && subject.totalScore && subject.targetScore
    )

    if (validSubjects.length === 0) {
      wx.showToast({
        title: '请至少添加一个完整的科目',
        icon: 'none'
      })
      return
    }

    // 构建更新的考试数据
    const updatedExam = {
      id: this.data.examId,
      name: examForm.name.trim(),
      type: examForm.type,
      description: examForm.description.trim(),
      date: examForm.date,
      startTime: examForm.startTime,
      endTime: examForm.endTime,
      targetScore: parseInt(examForm.targetScore) || 0,
      importance: examForm.importance,
      isActive: examForm.isActive,
      status: examForm.status,
      reminderEnabled: examForm.reminderEnabled,
      reminderFrequency: examForm.reminderFrequency,
      subjects: validSubjects.map(subject => ({
        name: subject.name.trim(),
        totalScore: parseInt(subject.totalScore) || 0,
        targetScore: parseInt(subject.targetScore) || 0
      })),
      updateTime: new Date().toISOString()
    }

    // 保存考试（这里应该保存到本地存储或服务器）
    this.saveExam(updatedExam)
  },

  // 保存考试
  async saveExam(examData) {
    wx.showLoading({
      title: '保存中...',
      mask: true
    })

    try {
      // 使用SmartApi更新考试
      const result = await SmartApi.updateExam(this.data.examId, {
        title: examData.name,
        subject: examData.type,
        description: examData.description,
        examDate: examData.date,
        examTime: examData.startTime,
        location: examData.location || '',
        reminderDays: examData.reminderEnabled ? 3 : 0
      })

      wx.hideLoading()

      if (result.success) {
        wx.showToast({
          title: '保存成功',
          icon: 'success'
        })

        // 返回上一页
        setTimeout(() => {
          wx.navigateBack()
        }, 1500)
      } else {
        wx.showToast({
          title: result.error || '保存失败',
          icon: 'none'
        })
      }

    } catch (error) {
      wx.hideLoading()
      console.error('保存考试失败:', error)
      wx.showToast({
        title: '保存失败，请重试',
        icon: 'none'
      })
    }
  }
})