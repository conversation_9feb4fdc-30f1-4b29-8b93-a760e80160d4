// 备考搭子管理云函数
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

exports.main = async (event, context) => {
  const { action, data } = event
  const { OPENID: userId } = cloud.getWXContext()

  console.log('studyGroupManager云函数调用:', { action, userId })

  try {
    switch (action) {
      case 'createGroup':
        return await createGroup(userId, data)
      case 'joinGroup':
        return await joinGroup(userId, data)
      case 'leaveGroup':
        return await leaveGroup(userId, data)
      case 'getMyGroups':
        return await getMyGroups(userId, data)
      case 'getGroupDetail':
        return await getGroupDetail(userId, data)
      case 'generateInviteCode':
        return await generateInviteCode(userId, data)
      case 'sharePlan':
        return await sharePlan(userId, data)
      case 'copyPlan':
        return await copyPlan(userId, data)
      case 'likePlan':
        return await likePlan(userId, data)
      case 'getGroupActivities':
        return await getGroupActivities(userId, data)
      case 'testDatabase':
        return await testDatabase(userId, data)
      case 'testShare':
        return await testShare(userId, data)
      default:
        return { success: false, error: '未知操作' }
    }
  } catch (error) {
    console.error('studyGroupManager云函数执行失败:', error)
    return { success: false, error: error.message }
  }
}

// 创建搭子小组
async function createGroup(userId, params) {
  const { examId, examName, groupName } = params || {}
  
  if (!examId || !examName) {
    return { success: false, error: '缺少必要参数' }
  }

  try {
    // 检查用户是否已经在该考试的小组中
    const existingGroup = await db.collection('study_groups')
      .where({
        examId: examId,
        'members.userId': userId,
        status: 'active'
      })
      .get()

    if (existingGroup.data.length > 0) {
      return { success: false, error: '您已经在该考试的搭子小组中' }
    }

    // 生成邀请码
    const inviteCode = generateRandomCode()

    // 获取用户信息
    const userInfo = await getUserInfo(userId)

    const groupData = {
      examId: examId,
      examName: examName,
      groupName: groupName || examName + '搭子小组',
      members: [
        {
          userId: userId,
          userInfo: userInfo,
          joinTime: new Date().toISOString(),
          role: 'member',
          isActive: true
        }
      ],
      maxMembers: 3,
      currentMembers: 1,
      creatorId: userId,
      createTime: new Date().toISOString(),
      updateTime: new Date().toISOString(),
      status: 'active',
      inviteCode: inviteCode,
      settings: {
        allowShare: true,
        allowInvite: true
      }
    }

    const result = await db.collection('study_groups').add({
      data: groupData
    })

    // 创建邀请记录
    await createInvitation(result._id, userId, inviteCode, examName)

    return { 
      success: true, 
      data: { 
        groupId: result._id, 
        inviteCode: inviteCode,
        ...groupData 
      } 
    }
  } catch (error) {
    console.error('创建搭子小组失败:', error)
    return { success: false, error: '创建失败' }
  }
}

// 加入搭子小组
async function joinGroup(userId, params) {
  const { inviteCode } = params || {}
  
  if (!inviteCode) {
    return { success: false, error: '邀请码不能为空' }
  }

  try {
    // 查找邀请记录
    const inviteResult = await db.collection('group_invitations')
      .where({
        inviteCode: inviteCode,
        status: 'pending'
      })
      .get()

    if (inviteResult.data.length === 0) {
      return { success: false, error: '邀请码无效或已过期' }
    }

    const invitation = inviteResult.data[0]
    
    // 检查邀请是否过期
    if (new Date(invitation.expireTime) < new Date()) {
      return { success: false, error: '邀请码已过期' }
    }

    // 查找小组
    const groupResult = await db.collection('study_groups')
      .where({
        _id: invitation.groupId,
        status: 'active'
      })
      .get()

    if (groupResult.data.length === 0) {
      return { success: false, error: '小组不存在或已解散' }
    }

    const group = groupResult.data[0]

    // 检查小组是否已满
    if (group.currentMembers >= group.maxMembers) {
      return { success: false, error: '小组人数已满' }
    }

    // 检查用户是否已在小组中
    const isAlreadyMember = group.members.some(member => member.userId === userId)
    if (isAlreadyMember) {
      return { success: false, error: '您已经在该小组中' }
    }

    // 获取用户信息
    const userInfo = await getUserInfo(userId)

    // 添加用户到小组
    const newMember = {
      userId: userId,
      userInfo: userInfo,
      joinTime: new Date().toISOString(),
      role: 'member',
      isActive: true
    }

    await db.collection('study_groups').doc(group._id).update({
      data: {
        members: db.command.push(newMember),
        currentMembers: db.command.inc(1),
        updateTime: new Date().toISOString()
      }
    })

    // 更新邀请状态
    await db.collection('group_invitations').doc(invitation._id).update({
      data: {
        status: 'accepted',
        usedBy: userId,
        usedTime: new Date().toISOString()
      }
    })

    // 创建加入动态
    await createActivity(group._id, userId, 'join_group', {})

    return { success: true, data: { groupId: group._id } }
  } catch (error) {
    console.error('加入搭子小组失败:', error)
    return { success: false, error: '加入失败' }
  }
}

// 退出搭子小组
async function leaveGroup(userId, params) {
  const { groupId } = params || {}
  
  if (!groupId) {
    return { success: false, error: '小组ID不能为空' }
  }

  try {
    // 查找小组
    const groupResult = await db.collection('study_groups').doc(groupId).get()
    
    if (!groupResult.data) {
      return { success: false, error: '小组不存在' }
    }

    const group = groupResult.data

    // 检查用户是否在小组中
    const memberIndex = group.members.findIndex(member => member.userId === userId)
    if (memberIndex === -1) {
      return { success: false, error: '您不在该小组中' }
    }

    // 移除用户
    const updatedMembers = group.members.filter(member => member.userId !== userId)

    // 如果小组只剩1人或没人，解散小组
    if (updatedMembers.length <= 1) {
      await db.collection('study_groups').doc(groupId).update({
        data: {
          status: 'disbanded',
          updateTime: new Date().toISOString()
        }
      })
    } else {
      await db.collection('study_groups').doc(groupId).update({
        data: {
          members: updatedMembers,
          currentMembers: updatedMembers.length,
          updateTime: new Date().toISOString()
        }
      })
    }

    return { success: true }
  } catch (error) {
    console.error('退出搭子小组失败:', error)
    return { success: false, error: '退出失败' }
  }
}

// 获取我的搭子小组
async function getMyGroups(userId, params) {
  try {
    const result = await db.collection('study_groups')
      .where({
        'members.userId': userId,
        status: 'active'
      })
      .orderBy('updateTime', 'desc')
      .get()

    return { success: true, data: result.data }
  } catch (error) {
    console.error('获取我的搭子小组失败:', error)
    return { success: false, error: '获取失败' }
  }
}

// 获取搭子小组详情
async function getGroupDetail(userId, params) {
  const { groupId } = params || {}

  if (!groupId) {
    return { success: false, error: '小组ID不能为空' }
  }

  try {
    // 获取小组信息
    const groupResult = await db.collection('study_groups').doc(groupId).get()

    if (!groupResult.data) {
      return { success: false, error: '小组不存在' }
    }

    const group = groupResult.data

    // 检查用户是否在小组中
    const isMember = group.members.some(member => member.userId === userId)
    if (!isMember) {
      return { success: false, error: '您不在该小组中' }
    }

    // 获取小组计划分享（添加错误处理）
    let shares = []
    try {
      const sharesResult = await db.collection('group_plan_shares')
        .where({
          groupId: groupId,
          status: 'active'
        })
        .orderBy('shareTime', 'desc')
        .limit(10)
        .get()
      shares = sharesResult.data || []
    } catch (error) {
      console.log('获取计划分享失败，可能集合不存在:', error.message)
      shares = []
    }

    // 获取小组动态（添加错误处理）
    let activities = []
    try {
      const activitiesResult = await db.collection('group_activities')
        .where({
          groupId: groupId
        })
        .orderBy('createTime', 'desc')
        .limit(20)
        .get()
      activities = activitiesResult.data || []
    } catch (error) {
      console.log('获取小组动态失败，可能集合不存在:', error.message)
      activities = []
    }

    return {
      success: true,
      data: {
        group: group,
        shares: shares,
        activities: activities
      }
    }
  } catch (error) {
    console.error('获取搭子小组详情失败:', error)
    return { success: false, error: '获取失败' }
  }
}

// 分享计划到小组
async function sharePlan(userId, params) {
  const { groupId, planData } = params || {}

  if (!groupId || !planData) {
    return { success: false, error: '缺少必要参数' }
  }

  try {
    // 检查用户是否在小组中
    const groupResult = await db.collection('study_groups').doc(groupId).get()

    if (!groupResult.data) {
      return { success: false, error: '小组不存在' }
    }

    const group = groupResult.data
    const isMember = group.members.some(member => member.userId === userId)
    if (!isMember) {
      return { success: false, error: '您不在该小组中' }
    }

    // 获取用户信息
    const userInfo = await getUserInfo(userId)

    // 创建分享记录
    const shareData = {
      groupId: groupId,
      sharerId: userId,
      sharerInfo: userInfo,
      examId: group.examId,
      planData: planData,
      shareTime: new Date().toISOString(),
      copyCount: 0,
      likes: [],
      status: 'active'
    }

    // 尝试添加分享记录
    let result
    try {
      result = await db.collection('group_plan_shares').add({
        data: shareData
      })
    } catch (addError) {
      console.error('添加分享记录失败:', addError)
      return { success: false, error: `添加分享记录失败: ${addError.message}` }
    }

    // 创建分享动态（如果失败不影响主流程）
    try {
      await createActivity(groupId, userId, 'plan_share', {
        planTitle: planData.title
      })
    } catch (activityError) {
      console.error('创建分享动态失败:', activityError)
      // 不影响主流程，继续执行
    }

    return { success: true, data: { shareId: result._id, ...shareData } }
  } catch (error) {
    console.error('分享计划失败:', error)
    return { success: false, error: `分享失败: ${error.message}` }
  }
}

// 复制小组计划
async function copyPlan(userId, params) {
  const { shareId } = params || {}

  if (!shareId) {
    return { success: false, error: '分享ID不能为空' }
  }

  try {
    // 获取分享记录
    const shareResult = await db.collection('group_plan_shares').doc(shareId).get()

    if (!shareResult.data) {
      return { success: false, error: '分享记录不存在' }
    }

    const share = shareResult.data

    // 检查用户是否在小组中
    const groupResult = await db.collection('study_groups').doc(share.groupId).get()
    const group = groupResult.data
    const isMember = group.members.some(member => member.userId === userId)
    if (!isMember) {
      return { success: false, error: '您不在该小组中' }
    }

    // 增加复制次数
    await db.collection('group_plan_shares').doc(shareId).update({
      data: {
        copyCount: db.command.inc(1)
      }
    })

    // 创建复制动态
    await createActivity(share.groupId, userId, 'copy_plan', {
      planTitle: share.planData.title,
      targetUserId: share.sharerId
    })

    return { success: true, data: share.planData }
  } catch (error) {
    console.error('复制计划失败:', error)
    return { success: false, error: '复制失败' }
  }
}

// 点赞小组计划
async function likePlan(userId, params) {
  const { shareId } = params || {}

  if (!shareId) {
    return { success: false, error: '分享ID不能为空' }
  }

  try {
    // 获取分享记录
    const shareResult = await db.collection('group_plan_shares').doc(shareId).get()

    if (!shareResult.data) {
      return { success: false, error: '分享记录不存在' }
    }

    const share = shareResult.data

    // 检查是否已经点赞
    const hasLiked = share.likes.some(like => like.userId === userId)

    if (hasLiked) {
      // 取消点赞
      const updatedLikes = share.likes.filter(like => like.userId !== userId)
      await db.collection('group_plan_shares').doc(shareId).update({
        data: {
          likes: updatedLikes
        }
      })
      return { success: true, data: { liked: false, likeCount: updatedLikes.length } }
    } else {
      // 添加点赞
      const newLike = {
        userId: userId,
        likeTime: new Date().toISOString()
      }
      await db.collection('group_plan_shares').doc(shareId).update({
        data: {
          likes: db.command.push(newLike)
        }
      })

      // 创建点赞动态
      await createActivity(share.groupId, userId, 'like_plan', {
        planTitle: share.planData.title,
        targetUserId: share.sharerId
      })

      return { success: true, data: { liked: true, likeCount: share.likes.length + 1 } }
    }
  } catch (error) {
    console.error('点赞计划失败:', error)
    return { success: false, error: '点赞失败' }
  }
}

// 获取小组动态
async function getGroupActivities(userId, params) {
  const { groupId, limit = 20 } = params || {}

  if (!groupId) {
    return { success: false, error: '小组ID不能为空' }
  }

  try {
    // 检查用户是否在小组中
    const groupResult = await db.collection('study_groups').doc(groupId).get()
    const group = groupResult.data
    const isMember = group.members.some(member => member.userId === userId)
    if (!isMember) {
      return { success: false, error: '您不在该小组中' }
    }

    // 获取动态
    const result = await db.collection('group_activities')
      .where({
        groupId: groupId
      })
      .orderBy('createTime', 'desc')
      .limit(limit)
      .get()

    return { success: true, data: result.data }
  } catch (error) {
    console.error('获取小组动态失败:', error)
    return { success: false, error: '获取失败' }
  }
}

// 辅助函数
function generateRandomCode() {
  return Math.random().toString(36).substr(2, 8).toUpperCase()
}

async function getUserInfo(userId) {
  try {
    const userResult = await db.collection('users').doc(userId).get()
    if (userResult.data) {
      return {
        nickName: userResult.data.nickName || '用户',
        avatarUrl: userResult.data.avatarUrl || ''
      }
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
  }
  
  return {
    nickName: '用户',
    avatarUrl: ''
  }
}

async function createInvitation(groupId, inviterId, inviteCode, examName) {
  const inviterInfo = await getUserInfo(inviterId)
  
  const invitationData = {
    groupId: groupId,
    inviterId: inviterId,
    inviterInfo: inviterInfo,
    inviteCode: inviteCode,
    examName: examName,
    createTime: new Date().toISOString(),
    expireTime: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7天后过期
    status: 'pending'
  }

  await db.collection('group_invitations').add({
    data: invitationData
  })
}

async function createActivity(groupId, userId, activityType, activityData) {
  try {
    const userInfo = await getUserInfo(userId)

    const activity = {
      groupId: groupId,
      userId: userId,
      userInfo: userInfo,
      activityType: activityType,
      activityData: activityData,
      createTime: new Date().toISOString(),
      isRead: false
    }

    await db.collection('group_activities').add({
      data: activity
    })
  } catch (error) {
    console.error('创建活动记录失败:', error)
    // 不抛出异常，避免影响主流程
  }
}

// 测试数据库连接
async function testDatabase(userId, params) {
  try {
    console.log('测试数据库连接，用户ID:', userId)

    // 测试study_groups集合
    const groupsResult = await db.collection('study_groups')
      .where({
        'members.userId': userId
      })
      .get()

    console.log('用户的小组数量:', groupsResult.data.length)
    console.log('用户的小组列表:', groupsResult.data)

    return {
      success: true,
      data: {
        userId: userId,
        groupCount: groupsResult.data.length,
        groups: groupsResult.data
      }
    }
  } catch (error) {
    console.error('测试数据库失败:', error)
    return {
      success: false,
      error: `数据库测试失败: ${error.message}`,
      details: error
    }
  }
}

// 测试分享功能
async function testShare(userId, params) {
  const { groupId, planData } = params || {}

  try {
    console.log('测试分享功能，用户ID:', userId)
    console.log('小组ID:', groupId)
    console.log('计划数据:', planData)

    // 检查小组是否存在
    const groupResult = await db.collection('study_groups').doc(groupId).get()

    if (!groupResult.data) {
      return { success: false, error: '小组不存在' }
    }

    const group = groupResult.data
    console.log('小组信息:', group)

    // 检查用户是否在小组中
    const isMember = group.members.some(member => member.userId === userId)
    if (!isMember) {
      return { success: false, error: '您不在该小组中' }
    }

    // 尝试创建分享记录（不实际保存）
    const shareData = {
      groupId: groupId,
      sharerId: userId,
      examId: group.examId,
      planData: planData || { title: '测试计划', totalTasks: 1 },
      shareTime: new Date().toISOString(),
      copyCount: 0,
      likes: [],
      status: 'active'
    }

    console.log('准备分享的数据:', shareData)

    return {
      success: true,
      data: {
        message: '分享测试成功',
        shareData: shareData,
        groupInfo: group
      }
    }
  } catch (error) {
    console.error('测试分享失败:', error)
    return {
      success: false,
      error: `测试分享失败: ${error.message}`,
      details: error
    }
  }
}
