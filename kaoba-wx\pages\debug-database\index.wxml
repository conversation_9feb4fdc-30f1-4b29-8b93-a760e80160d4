<!-- 数据库调试页面 -->
<view class="debug-container">
  <view class="header">
    <text class="title">🔧 数据库调试工具</text>
    <text class="subtitle">检查和管理云数据库状态</text>
  </view>

  <!-- 操作按钮 -->
  <view class="actions">
    <button class="action-btn primary" bindtap="checkDatabaseStatus" disabled="{{loading}}">
      {{loading ? '检查中...' : '检查状态'}}
    </button>
    
    <button class="action-btn secondary" bindtap="listCollections" disabled="{{loading}}">
      查看集合
    </button>
    
    <button class="action-btn success" bindtap="createSampleData" disabled="{{loading}}">
      创建示例数据
    </button>

    <button class="action-btn info" bindtap="forceInitDatabase" disabled="{{loading}}">
      强制初始化
    </button>

    <button class="action-btn warning" bindtap="reinitDatabase" disabled="{{loading}}">
      重新初始化
    </button>

    <button class="action-btn danger" bindtap="resetDatabase" disabled="{{loading}}">
      重置数据库
    </button>
  </view>

  <!-- 数据库状态 -->
  <view class="status-section" wx:if="{{dbStatus}}">
    <view class="section-title">📊 数据库状态</view>
    
    <view class="status-card">
      <view class="status-item">
        <text class="label">连接状态:</text>
        <text class="value {{dbStatus.data.connection.success ? 'success' : 'error'}}">
          {{dbStatus.data.connection.success ? '✅ 正常' : '❌ 失败'}}
        </text>
      </view>
      
      <view class="status-item" wx:if="{{dbStatus.data.database.success}}">
        <text class="label">数据统计:</text>
        <view class="stats">
          <view class="stat-item">
            <text class="stat-label">任务:</text>
            <text class="stat-value">{{dbStatus.data.database.data.tasks}}</text>
          </view>
          <view class="stat-item">
            <text class="stat-label">考试:</text>
            <text class="stat-value">{{dbStatus.data.database.data.exams}}</text>
          </view>
          <view class="stat-item">
            <text class="stat-label">学习会话:</text>
            <text class="stat-value">{{dbStatus.data.database.data.studySessions}}</text>
          </view>
          <view class="stat-item">
            <text class="stat-label">番茄钟:</text>
            <text class="stat-value">{{dbStatus.data.database.data.pomodoroSessions}}</text>
          </view>
          <view class="stat-item total">
            <text class="stat-label">总计:</text>
            <text class="stat-value">{{dbStatus.data.database.data.total}}</text>
          </view>
        </view>
      </view>
      
      <view class="status-item" wx:if="{{!dbStatus.data.database.success}}">
        <text class="label">数据库错误:</text>
        <text class="value error">{{dbStatus.data.database.error}}</text>
      </view>
    </view>
  </view>

  <!-- 操作日志 -->
  <view class="logs-section">
    <view class="section-header">
      <text class="section-title">📝 操作日志</text>
      <view class="log-actions">
        <button class="log-btn" bindtap="clearLogs" size="mini">清空</button>
        <button class="log-btn" bindtap="copyLogs" size="mini">复制</button>
      </view>
    </view>
    
    <scroll-view class="logs-container" scroll-y="true">
      <view class="log-item" wx:for="{{logs}}" wx:key="index">
        {{item}}
      </view>
      
      <view class="empty-logs" wx:if="{{logs.length === 0}}">
        暂无日志记录
      </view>
    </scroll-view>
  </view>

  <!-- 使用说明 -->
  <view class="help-section">
    <view class="section-title">💡 使用说明</view>
    <view class="help-content">
      <view class="help-item">
        <text class="help-title">检查状态:</text>
        <text class="help-desc">查看数据库连接和数据统计</text>
      </view>
      <view class="help-item">
        <text class="help-title">查看集合:</text>
        <text class="help-desc">检查各个数据集合是否存在</text>
      </view>
      <view class="help-item">
        <text class="help-title">创建示例数据:</text>
        <text class="help-desc">手动创建示例数据到数据库</text>
      </view>
      <view class="help-item">
        <text class="help-title">强制初始化:</text>
        <text class="help-desc">强制创建所有缺失的集合和数据</text>
      </view>
      <view class="help-item">
        <text class="help-title">重新初始化:</text>
        <text class="help-desc">重新运行数据库初始化流程</text>
      </view>
      <view class="help-item">
        <text class="help-title">重置数据库:</text>
        <text class="help-desc">清空所有数据并重新创建</text>
      </view>
    </view>
  </view>
</view>
