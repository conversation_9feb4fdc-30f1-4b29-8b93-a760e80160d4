<!--pages/home/<USER>
<view class="container">
  <!-- 顶部问候 -->
  <view class="greeting-section">
    <view class="greeting-card">
      <view class="greeting-content">
        <view class="greeting-text">
          <text class="greeting-time">{{greetingTime}}</text>
          <text class="greeting-message">{{greetingMessage}}</text>
        </view>
        <view class="user-avatar" wx:if="{{userInfo}}">
          <image class="avatar-img" src="{{userInfo.avatarUrl || '/images/default-avatar.png'}}" mode="aspectFill"></image>
        </view>
      </view>
      <view class="date-info">
        <text class="current-date">{{currentDate}}</text>
        <text class="lunar-date">{{lunarDate}}</text>
      </view>
    </view>
  </view>

  <!-- 考试倒计时 -->
  <view class="countdown-section" wx:if="{{nearestExam}}">
    <view class="countdown-card">
      <view class="countdown-header">
        <view class="exam-info">
          <text class="exam-name">{{nearestExam.name}}</text>
          <text class="exam-date">{{nearestExam.date}}</text>
        </view>
        <view class="countdown-status">
          <text class="status-text">{{nearestExam.status}}</text>
        </view>
      </view>
      <view class="countdown-display">
        <view class="countdown-item" wx:for="{{countdown}}" wx:key="unit">
          <text class="countdown-number">{{item.value}}</text>
          <text class="countdown-unit">{{item.unit}}</text>
        </view>
      </view>
      <view class="countdown-progress">
        <view class="progress-bar">
          <view class="progress-fill" style="width: {{nearestExam.preparationProgress}}%"></view>
        </view>
        <text class="progress-text">准备度 {{nearestExam.preparationProgress}}%</text>
      </view>
      <view class="countdown-actions">
        <button class="btn btn-primary" bindtap="viewExamDetail">查看详情</button>
        <button class="btn btn-outline" bindtap="startStudy">开始复习</button>
      </view>
    </view>
  </view>

  <!-- 今日复习 -->
  <view class="tasks-section">
    <view class="section-header">
      <text class="section-title">今日复习</text>
      <view class="section-actions">
        <text class="task-progress">{{completedTasks}}/{{totalTasks}}</text>
        <text class="view-all" bindtap="viewAllTasks">查看全部</text>
      </view>
    </view>
    <view class="tasks-list" wx:if="{{todayTasks.length > 0}}">
      <view class="task-item" wx:for="{{todayTasks}}" wx:key="id" bindtap="viewTaskDetail" data-id="{{item.id}}">
        <view class="task-checkbox" bindtap="toggleTask" data-id="{{item.id}}" catchtap="true">
          <text class="checkbox-icon">{{item.completed ? '✅' : '⭕'}}</text>
        </view>
        <view class="task-content">
          <text class="task-title {{item.completed ? 'completed' : ''}}">{{item.title}}</text>
          <view class="task-meta">
            <text class="task-subject">{{item.subject}}</text>
            <text class="task-priority priority-{{item.priority}}">{{item.priorityText}}</text>
            <text class="task-time" wx:if="{{item.estimatedTime}}">{{item.estimatedTime}}</text>
          </view>
        </view>
        <view class="task-actions">
          <text class="task-status">{{item.statusText}}</text>
        </view>
      </view>
    </view>
    <view class="empty-tasks" wx:else>
      <text class="empty-icon">📝</text>
      <text class="empty-text">今天还没有复习计划</text>
      <button class="btn btn-primary" bindtap="addTask">制定复习计划</button>
    </view>
  </view>

  <!-- 备考搭子 -->
  <view class="study-group-section">
    <view class="section-header">
      <text class="section-title">备考搭子</text>
      <text class="view-all" wx:if="{{hasStudyGroup && studyGroups.length > 0}}" bindtap="onViewStudyGroup" data-group-id="{{studyGroups[0].id}}">查看详情</text>
    </view>

    <!-- 有搭子时显示 -->
    <view class="study-group-content" wx:if="{{hasStudyGroup}}">
      <view class="study-group-item" wx:for="{{studyGroups}}" wx:key="id" bindtap="onViewStudyGroup" data-group-id="{{item.id}}">
        <view class="group-header">
          <text class="group-name">{{item.examName}}</text>
          <text class="member-count">{{item.memberCount}}/{{item.maxMembers}}人</text>
        </view>
        <view class="group-members">
          <view class="member-avatar" wx:for="{{item.members}}" wx:for-item="member" wx:key="userId">
            <image src="{{member.userInfo.avatarUrl || '/images/default-avatar.png'}}" class="avatar-img" />
            <text class="member-name">{{member.userInfo.nickName}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 无搭子时显示 -->
    <view class="no-study-group" wx:else>
      <text class="empty-icon">👥</text>
      <text class="empty-text">还没有备考搭子</text>
      <view class="group-actions">
        <button class="btn btn-primary" bindtap="onCreateStudyGroup">创建小组</button>
        <button class="btn btn-secondary" bindtap="onJoinStudyGroup">加入小组</button>
      </view>
    </view>
  </view>

  <!-- 复习统计 -->
  <view class="stats-section">
    <view class="section-header">
      <text class="section-title">今日复习</text>
      <text class="view-all" bindtap="viewDataCenter">查看详情</text>
    </view>
    <view class="stats-grid">
      <view class="stat-item" wx:for="{{todayStats}}" wx:key="label">
        <text class="stat-value">{{item.value}}</text>
        <text class="stat-label">{{item.label}}</text>
        <view class="stat-icon" style="background-color: {{item.color}}">
          <text>{{item.icon}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 快捷操作 -->
  <view class="quick-actions-section">
    <view class="section-header">
      <text class="section-title">快捷操作</text>
    </view>
    <view class="quick-actions-grid">
      <view class="quick-action-item" wx:for="{{quickActions}}" wx:key="id" bindtap="executeQuickAction" data-action="{{item.action}}">
        <view class="action-icon" style="background-color: {{item.bgColor}}">
          <text>{{item.icon}}</text>
        </view>
        <text class="action-label">{{item.label}}</text>
      </view>
    </view>
  </view>

  <!-- 最近活动 -->
  <view class="recent-section" wx:if="{{recentActivities.length > 0}}">
    <view class="section-header">
      <text class="section-title">最近活动</text>
    </view>
    <view class="recent-list">
      <view class="recent-item" wx:for="{{recentActivities}}" wx:key="id">
        <view class="recent-icon">
          <text>{{item.icon}}</text>
        </view>
        <view class="recent-content">
          <text class="recent-title">{{item.title}}</text>
          <text class="recent-time">{{item.time}}</text>
        </view>
        <view class="recent-status">
          <text class="status-badge status-{{item.status}}">{{item.statusText}}</text>
        </view>
      </view>
    </view>
  </view>
</view>

<!-- 悬浮按钮 -->
<view class="fab-container">
  <view class="fab-menu" wx:if="{{fabExpanded}}">
    <view class="fab-menu-item" wx:for="{{fabMenuItems}}" wx:key="id" bindtap="executeFabAction" data-action="{{item.action}}">
      <view class="fab-item-label">{{item.label}}</view>
      <view class="fab-item-button" style="background-color: {{item.color}}">
        <text>{{item.icon}}</text>
      </view>
    </view>
  </view>
  <button class="fab-main-button {{fabExpanded ? 'expanded' : ''}}" bindtap="toggleFab">
    <text>+</text>
  </button>
</view>
