// pages/achievement-system/index.js
const SmartApi = require('../../utils/smartApi')

Page({
  data: {
    overviewStats: {
      totalUnlocked: 12,
      totalAchievements: 25,
      completionRate: 48,
      totalPoints: 1250
    },

    recentAchievements: [
      {
        id: 'streak_15',
        name: '坚持达人',
        icon: '🔥',
        color: '#FF4D4F',
        unlockedTime: '2小时前',
        isNew: true
      },
      {
        id: 'task_master',
        name: '任务大师',
        icon: '🎯',
        color: '#52C41A',
        unlockedTime: '昨天',
        isNew: false
      }
    ],

    selectedCategory: 'study',
    achievementCategories: [
      { id: 'study', name: '学习成就' },
      { id: 'focus', name: '专注成就' },
      { id: 'exam', name: '考试成就' },
      { id: 'habit', name: '习惯成就' },
      { id: 'special', name: '特殊成就' }
    ],

    currentCategoryInfo: {
      name: '学习成就',
      unlockedCount: 5,
      totalCount: 10,
      progress: 50
    },

    currentCategoryAchievements: [],

    achievementStats: [
      { label: '学习成就', value: '5/10', color: '#1890FF', bgColor: '#E6F7FF' },
      { label: '专注成就', value: '3/6', color: '#52C41A', bgColor: '#F6FFED' },
      { label: '考试成就', value: '2/5', color: '#FA8C16', bgColor: '#FFF7E6' },
      { label: '习惯成就', value: '2/4', color: '#722ED1', bgColor: '#F9F0FF' }
    ],

    allAchievements: {},
    showAchievementDetail: false,
    selectedAchievement: {}
  },

  async onLoad() {
    wx.showLoading({
      title: '加载中...',
      mask: true
    })

    try {
      await this.initAchievements()
      await this.loadAchievementData()
      await this.updateCategoryAchievements()

      wx.hideLoading()
    } catch (error) {
      wx.hideLoading()
      console.error('加载成就系统失败:', error)
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      })
    }
  },

  async onShow() {
    await this.refreshAchievementProgress()
  },

  // 初始化成就数据
  async initAchievements() {
    try {
      // 调用云函数初始化成就数据
      await wx.cloud.callFunction({
        name: 'achievementManager',
        data: {
          action: 'initAchievements',
          data: {}
        }
      })
    } catch (error) {
      console.error('初始化成就数据失败:', error)
    }
  },

  // 加载成就数据
  async loadAchievementData() {
    try {
      // 获取成就统计
      const statsResult = await SmartApi.getAchievementStats()

      if (statsResult.success && statsResult.data) {
        const achievementStats = statsResult.data.map(stat => ({
          label: stat.name,
          value: `${stat.unlocked}/${stat.total}`,
          color: this.getCategoryColor(stat.category),
          bgColor: this.getCategoryBgColor(stat.category)
        }))

        this.setData({ achievementStats })
      }

      // 获取用户成就
      const achievementsResult = await SmartApi.getUserAchievements()

      if (achievementsResult.success && achievementsResult.data) {
        // 按分类组织成就数据
        const allAchievements = {}
        const recentAchievements = []

        achievementsResult.data.forEach(achievement => {
          const category = achievement.category
          if (!allAchievements[category]) {
            allAchievements[category] = []
          }
          allAchievements[category].push(achievement)

          // 收集最近解锁的成就
          if (achievement.unlocked && achievement.isNew) {
            recentAchievements.push({
              id: achievement._id,
              name: achievement.name,
              icon: achievement.icon,
              color: achievement.color,
              unlockedTime: this.formatRelativeTime(achievement.unlockedTime),
              isNew: achievement.isNew
            })
          }
        })

        this.setData({
          allAchievements,
          recentAchievements: recentAchievements.slice(0, 3) // 只显示最近3个
        })
      }
    } catch (error) {
      console.error('加载成就数据失败:', error)
      this.setDefaultAchievementData()
    }
  },

  // 设置默认成就数据
  setDefaultAchievementData() {
    const achievements = {
      study: [
        {
          id: 'first_task',
          name: '初学者',
          description: '完成第一个学习任务',
          icon: '🌱',
          color: '#52C41A',
          category: 'study',
          categoryName: '学习成就',
          rarity: '普通',
          rarityColor: '#52C41A',
          points: 10,
          unlocked: true,
          unlockedTime: '2025-01-20 14:30',
          reward: '解锁学习统计功能'
        },
        {
          id: 'task_master',
          name: '任务大师',
          description: '累计完成100个学习任务',
          icon: '🎯',
          color: '#1890FF',
          category: 'study',
          categoryName: '学习成就',
          rarity: '稀有',
          rarityColor: '#1890FF',
          points: 50,
          unlocked: true,
          unlockedTime: '2025-01-25 16:45',
          currentValue: 100,
          targetValue: 100,
          progress: 100,
          reward: '解锁高级任务模板'
        },
        {
          id: 'knowledge_seeker',
          name: '求知者',
          description: '学习时长达到100小时',
          icon: '📚',
          color: '#722ED1',
          category: 'study',
          categoryName: '学习成就',
          rarity: '史诗',
          rarityColor: '#722ED1',
          points: 100,
          unlocked: false,
          currentValue: 75,
          targetValue: 100,
          progress: 75,
          tips: '继续保持学习，还需要25小时即可解锁'
        }
      ],
      focus: [
        {
          id: 'focus_beginner',
          name: '专注新手',
          description: '完成第一个番茄钟',
          icon: '🍅',
          color: '#FF4D4F',
          category: 'focus',
          categoryName: '专注成就',
          rarity: '普通',
          rarityColor: '#52C41A',
          points: 10,
          unlocked: true,
          unlockedTime: '2025-01-18 10:15'
        },
        {
          id: 'streak_master',
          name: '坚持达人',
          description: '连续学习15天',
          icon: '🔥',
          color: '#FA8C16',
          category: 'focus',
          categoryName: '专注成就',
          rarity: '稀有',
          rarityColor: '#1890FF',
          points: 75,
          unlocked: true,
          unlockedTime: '2025-01-27 09:00'
        }
      ],
      exam: [
        {
          id: 'exam_planner',
          name: '考试规划师',
          description: '创建第一个考试计划',
          icon: '📋',
          color: '#1890FF',
          category: 'exam',
          categoryName: '考试成就',
          rarity: '普通',
          rarityColor: '#52C41A',
          points: 15,
          unlocked: true,
          unlockedTime: '2025-01-15 11:30'
        }
      ]
    }

    this.setData({ allAchievements: achievements })
  },

  // 加载成就数据
  loadAchievementData() {
    try {
      // 从本地存储加载用户成就数据
      const userAchievements = wx.getStorageSync('userAchievements') || {}

      // 更新成就状态
      this.updateAchievementStatus(userAchievements)

      // 计算统计数据
      this.calculateOverviewStats()

    } catch (error) {
      console.error('加载成就数据失败:', error)
    }
  },

  // 更新成就状态
  updateAchievementStatus(userAchievements) {
    const allAchievements = { ...this.data.allAchievements }

    Object.keys(allAchievements).forEach(category => {
      allAchievements[category] = allAchievements[category].map(achievement => {
        const userAchievement = userAchievements[achievement.id]
        if (userAchievement) {
          return {
            ...achievement,
            ...userAchievement
          }
        }
        return achievement
      })
    })

    this.setData({ allAchievements })
  },

  // 计算概览统计
  calculateOverviewStats() {
    const allAchievements = this.data.allAchievements
    let totalAchievements = 0
    let totalUnlocked = 0
    let totalPoints = 0

    Object.keys(allAchievements).forEach(category => {
      allAchievements[category].forEach(achievement => {
        totalAchievements++
        if (achievement.unlocked) {
          totalUnlocked++
          totalPoints += achievement.points || 0
        }
      })
    })

    const completionRate = totalAchievements > 0 ? Math.round((totalUnlocked / totalAchievements) * 100) : 0

    this.setData({
      'overviewStats.totalAchievements': totalAchievements,
      'overviewStats.totalUnlocked': totalUnlocked,
      'overviewStats.completionRate': completionRate,
      'overviewStats.totalPoints': totalPoints
    })
  },

  // 切换成就分类
  switchCategory(e) {
    const category = e.currentTarget.dataset.category
    this.setData({ selectedCategory: category })
    this.updateCategoryAchievements()
  },

  // 更新分类成就
  updateCategoryAchievements() {
    const { selectedCategory, allAchievements, achievementCategories } = this.data
    const categoryAchievements = allAchievements[selectedCategory] || []

    // 计算分类统计
    const totalCount = categoryAchievements.length
    const unlockedCount = categoryAchievements.filter(item => item.unlocked).length
    const progress = totalCount > 0 ? Math.round((unlockedCount / totalCount) * 100) : 0

    const categoryInfo = achievementCategories.find(cat => cat.id === selectedCategory)

    this.setData({
      currentCategoryAchievements: categoryAchievements,
      currentCategoryInfo: {
        name: categoryInfo ? categoryInfo.name : '',
        unlockedCount,
        totalCount,
        progress
      }
    })
  },

  // 查看成就详情
  viewAchievementDetail(e) {
    const achievement = e.currentTarget.dataset.achievement
    this.setData({
      selectedAchievement: achievement,
      showAchievementDetail: true
    })
  },

  // 隐藏成就详情
  hideAchievementDetail() {
    this.setData({ showAchievementDetail: false })
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止事件冒泡
  },

  // 刷新成就进度
  refreshAchievementProgress() {
    // 检查是否有新解锁的成就
    this.checkNewAchievements()

    // 重新计算统计数据
    this.calculateOverviewStats()
    this.updateCategoryAchievements()
  },

  // 检查新成就
  checkNewAchievements() {
    try {
      // 获取学习数据
      const studyRecords = wx.getStorageSync('studyRecords') || []
      const todayStats = wx.getStorageSync('todayStats') || {}

      // 检查各种成就条件
      this.checkStudyAchievements(studyRecords)
      this.checkFocusAchievements(studyRecords)
      this.checkHabitAchievements(todayStats)

    } catch (error) {
      console.error('检查新成就失败:', error)
    }
  },

  // 检查学习成就
  checkStudyAchievements(studyRecords) {
    const completedTasks = studyRecords.filter(record => record.type === 'task').length
    const totalStudyHours = studyRecords.reduce((total, record) => {
      const duration = parseInt(record.duration) || 0
      return total + duration
    }, 0)

    // 检查任务大师成就
    if (completedTasks >= 100) {
      this.unlockAchievement('task_master')
    }

    // 检查求知者成就
    if (totalStudyHours >= 100) {
      this.unlockAchievement('knowledge_seeker')
    }
  },

  // 检查专注成就
  checkFocusAchievements(studyRecords) {
    const pomodoroRecords = studyRecords.filter(record => record.type === 'pomodoro')

    // 检查专注新手成就
    if (pomodoroRecords.length >= 1) {
      this.unlockAchievement('focus_beginner')
    }

    // 检查连续学习成就
    const streak = this.calculateStudyStreak(studyRecords)
    if (streak >= 15) {
      this.unlockAchievement('streak_master')
    }
  },

  // 检查习惯成就
  checkHabitAchievements(todayStats) {
    // 根据今日统计检查习惯相关成就
    if (todayStats.completedPomodoros >= 5) {
      this.unlockAchievement('daily_focus_master')
    }
  },

  // 计算学习连续天数
  calculateStudyStreak(studyRecords) {
    // 简化实现，实际应该计算连续学习天数
    return 15 // 模拟返回
  },

  // 解锁成就
  unlockAchievement(achievementId) {
    try {
      const userAchievements = wx.getStorageSync('userAchievements') || {}

      // 如果成就已解锁，跳过
      if (userAchievements[achievementId] && userAchievements[achievementId].unlocked) {
        return
      }

      // 解锁成就
      userAchievements[achievementId] = {
        unlocked: true,
        unlockedTime: new Date().toLocaleString()
      }

      // 保存到本地存储
      wx.setStorageSync('userAchievements', userAchievements)

      // 显示解锁提示
      this.showAchievementUnlocked(achievementId)

      // 刷新页面数据
      this.loadAchievementData()

    } catch (error) {
      console.error('解锁成就失败:', error)
    }
  },

  // 显示成就解锁提示
  showAchievementUnlocked(achievementId) {
    // 找到成就信息
    let achievement = null
    Object.keys(this.data.allAchievements).forEach(category => {
      const found = this.data.allAchievements[category].find(item => item.id === achievementId)
      if (found) {
        achievement = found
      }
    })

    if (achievement) {
      wx.showToast({
        title: `🏆 解锁成就：${achievement.name}`,
        icon: 'none',
        duration: 3000
      })
    }
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.refreshAchievementProgress()
    wx.stopPullDownRefresh()
  },

  // 分享成就
  onShareAppMessage() {
    const { overviewStats } = this.data
    return {
      title: `我已经解锁了${overviewStats.totalUnlocked}个学习成就！`,
      path: '/pages/achievement-system/index',
      imageUrl: '/images/share-achievements.png'
    }
  }
})