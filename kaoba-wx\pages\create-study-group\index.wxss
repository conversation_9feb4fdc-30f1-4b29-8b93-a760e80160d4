/* pages/create-study-group/index.wxss */
.container {
  padding: 20rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

/* 页面标题 */
.page-header {
  text-align: center;
  padding: 40rpx 20rpx;
  background: white;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
}

.page-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 12rpx;
}

.page-subtitle {
  font-size: 28rpx;
  color: #666;
}

/* 区块样式 */
.section {
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

/* 考试列表 */
.exam-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.exam-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.exam-item.selected {
  background: #e3f2fd;
  border-color: #1890ff;
}

.exam-info {
  flex: 1;
}

.exam-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.exam-subject {
  font-size: 24rpx;
  color: #666;
}

.exam-date {
  margin-right: 16rpx;
}

.exam-date text {
  font-size: 24rpx;
  color: #999;
}

.check-icon {
  width: 40rpx;
  height: 40rpx;
  background: #1890ff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.check-icon text {
  color: white;
  font-size: 24rpx;
  font-weight: bold;
}

/* 空状态 */
.empty-exams {
  text-align: center;
  padding: 60rpx 20rpx;
}

.empty-icon {
  font-size: 80rpx;
  display: block;
  margin-bottom: 16rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  display: block;
  margin-bottom: 32rpx;
}

/* 输入框 */
.input-group {
  margin-bottom: 12rpx;
}

.form-input {
  width: 100%;
  padding: 24rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  font-size: 30rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.form-input:focus {
  background: white;
  border-color: #1890ff;
}

.input-tip {
  padding-left: 8rpx;
}

.input-tip text {
  font-size: 24rpx;
  color: #999;
}

/* 小组信息 */
.group-info {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.info-icon {
  font-size: 32rpx;
  width: 48rpx;
  text-align: center;
}

.info-text {
  font-size: 28rpx;
  color: #666;
}

/* 底部按钮 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 24rpx;
  background: white;
  border-top: 1rpx solid #eee;
}

.btn-large {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  font-size: 32rpx;
}

/* 按钮样式 */
.btn {
  border: none;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  text-align: center;
  transition: all 0.3s ease;
}

.btn-primary {
  background: #1890ff;
  color: white;
}

.btn-primary:disabled {
  background: #ccc;
  color: #999;
}

.btn-secondary {
  background: #f0f0f0;
  color: #666;
}

.btn-secondary:active {
  background: #e0e0e0;
}
