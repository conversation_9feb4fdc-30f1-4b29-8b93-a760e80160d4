// pages/profile/index.js
const LoginApi = require('../../utils/loginApi')

Page({
  data: {
    userInfo: {},
    userStats: [],
    recentAchievements: [],
    menuSections: [],
    dataCards: [],
    quickActions: [],
    version: '1.0.0',
    showEditModal: false,
    editUserInfo: {},
    isLoggedIn: false,

    userStats: [
      { label: '复习时长', value: '125h' },
      { label: '完成复习', value: '68' },
      { label: '番茄钟', value: '156' },
      { label: '备考天数', value: '45' }
    ]
  },

  onLoad() {
    this.checkLoginStatus()
    this.initPage()
  },

  onShow() {
    this.checkLoginStatus()
    this.loadData()
    // 更新tabBar选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 4
      })
    }
  },

  // 检查登录状态
  checkLoginStatus() {
    const app = getApp()
    const loginStatus = app.checkLoginStatus()

    this.setData({
      isLoggedIn: loginStatus.isLoggedIn,
      userInfo: loginStatus.userInfo || {}
    })

    // 重新初始化快捷操作（因为登录状态可能改变）
    this.initQuickActions()

    return loginStatus.isLoggedIn
  },

  // 初始化页面
  initPage() {
    this.initMenuSections()
    this.initQuickActions()
    this.loadUserInfo()
  },

  // 初始化菜单
  initMenuSections() {
    const menuSections = [
      {
        title: '备考管理',
        items: [
          {
            id: 'data_center',
            icon: '📈',
            text: '数据中心',
            action: 'dataCenter'
          },
          {
            id: 'achievement',
            icon: '🏆',
            text: '成就系统',
            action: 'achievement'
          }
        ]
      },
      {
        title: '应用设置',
        items: [
          {
            id: 'settings',
            icon: '⚙️',
            text: '设置',
            action: 'settings'
          },
          {
            id: 'notification',
            icon: '🔔',
            text: '通知设置',
            action: 'notification',
            badge: '3'
          },
          {
            id: 'theme',
            icon: '🎨',
            text: '主题外观',
            action: 'theme'
          }
        ]
      },
      {
        title: '帮助支持',
        items: [
          {
            id: 'help',
            icon: '❓',
            text: '帮助中心',
            action: 'help'
          },
          {
            id: 'feedback',
            icon: '💬',
            text: '意见反馈',
            action: 'feedback'
          },
          {
            id: 'about',
            icon: 'ℹ️',
            text: '关于我们',
            action: 'about'
          }
        ]
      }
    ]

    this.setData({ menuSections })
  },

  // 初始化快捷操作
  initQuickActions() {
    const { isLoggedIn } = this.data

    const quickActions = [
      {
        id: 'backup',
        icon: '☁️',
        text: '数据备份',
        bgColor: '#E6F7FF',
        action: 'backup'
      },
      {
        id: 'update',
        icon: '🔄',
        text: '检查更新',
        bgColor: '#F9F0FF',
        action: 'update'
      }
    ]

    // 根据登录状态添加登录/退出选项
    if (isLoggedIn) {
      quickActions.push({
        id: 'admin',
        icon: '⚙️',
        text: '数据管理',
        bgColor: '#FFF0F6',
        action: 'admin'
      })
      quickActions.push({
        id: 'logout',
        icon: '🚪',
        text: '退出登录',
        bgColor: '#FFF1F0',
        action: 'logout'
      })
    } else {
      quickActions.push({
        id: 'login',
        icon: '🔑',
        text: '立即登录',
        bgColor: '#F0F9FF',
        action: 'login'
      })
    }

    this.setData({ quickActions })
  },

  // 加载用户信息
  loadUserInfo() {
    const app = getApp()
    let userInfo = app.globalData.userInfo || wx.getStorageSync('kaoba_user_info') || {}

    // 确保所有字段都有默认值，避免null值
    userInfo = {
      nickName: userInfo.nickName || '考试达人',
      currentExam: userInfo.currentExam || '暂未设置考试',
      avatarUrl: userInfo.avatarUrl || '',
      signature: userInfo.signature || '努力备考，金榜题名！',
      ...userInfo
    }

    this.setData({ userInfo })
  },

  // 加载数据
  loadData() {
    this.loadUserStats()
    this.loadRecentAchievements()
    this.loadDataCards()
  },

  // 加载用户统计
  loadUserStats() {
    const userStats = [
      { label: '备考天数', value: '128' },
      { label: '完成复习', value: '256' },
      { label: '专注时长', value: '89h' },
      { label: '获得成就', value: '15' }
    ]

    this.setData({ userStats })
  },

  // 加载最近成就
  loadRecentAchievements() {
    const recentAchievements = [
      { id: 'focus_master', icon: '🎯', name: '专注大师' },
      { id: 'task_killer', icon: '⚡', name: '复习达人' },
      { id: 'early_bird', icon: '🌅', name: '早起鸟儿' },
      { id: 'night_owl', icon: '🦉', name: '夜猫子' }
    ]

    this.setData({ recentAchievements })
  },

  // 加载数据卡片
  loadDataCards() {
    const dataCards = []

    this.setData({ dataCards })
  },

  // 编辑个人资料
  editProfile() {
    this.setData({
      editUserInfo: { ...this.data.userInfo },
      showEditModal: true
    })
  },

  // 隐藏编辑弹窗
  hideEditModal() {
    this.setData({ showEditModal: false })
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止事件冒泡
  },

  // 更新昵称
  updateNickname(e) {
    this.setData({
      'editUserInfo.nickname': e.detail.value
    })
  },

  // 更新当前考试
  updateCurrentExam(e) {
    this.setData({
      'editUserInfo.currentExam': e.detail.value
    })
  },

  // 更新个性签名
  updateSignature(e) {
    this.setData({
      'editUserInfo.signature': e.detail.value
    })
  },

  // 保存用户信息
  saveUserInfo() {
    const { editUserInfo } = this.data

    // 验证必填字段
    if (!editUserInfo.nickname || !editUserInfo.nickname.trim()) {
      wx.showToast({
        title: '请输入昵称',
        icon: 'none'
      })
      return
    }

    // 保存到本地存储
    wx.setStorageSync('userInfo', editUserInfo)

    // 更新页面数据
    this.setData({
      userInfo: editUserInfo,
      showEditModal: false
    })

    wx.showToast({
      title: '保存成功',
      icon: 'success'
    })
  },

  // 处理菜单点击
  handleMenuTap(e) {
    const action = e.currentTarget.dataset.action
    this.handleAction(action)
  },

  // 处理快捷操作
  handleQuickAction(e) {
    const action = e.currentTarget.dataset.action
    this.handleAction(action)
  },

  // 处理操作
  handleAction(action) {
    switch (action) {

      case 'dataCenter':
        wx.navigateTo({
          url: '/pages/data-center/index'
        })
        break
      case 'dataExport':
        wx.navigateTo({
          url: '/pages/data-export/index'
        })
        break
      case 'achievement':
        wx.navigateTo({
          url: '/pages/achievement-system/index'
        })
        break
      case 'settings':
        wx.navigateTo({
          url: '/pages/settings/index'
        })
        break
      case 'help':
        wx.navigateTo({
          url: '/pages/help-feedback/index'
        })
        break
      case 'feedback':
        wx.navigateTo({
          url: '/pages/help-feedback/index?tab=feedback'
        })
        break
      case 'share':
        this.shareApp()
        break
      case 'backup':
        this.backupData()
        break
      case 'rate':
        this.rateApp()
        break
      case 'update':
        this.checkUpdate()
        break
      case 'login':
        this.login()
        break
      case 'logout':
        this.logout()
        break
      case 'admin':
        wx.navigateTo({
          url: '/pages/admin/index'
        })
        break
      default:
        wx.showToast({
          title: '功能开发中',
          icon: 'none'
        })
    }
  },

  // 查看所有成就
  viewAllAchievements() {
    wx.navigateTo({
      url: '/pages/achievement-system/index'
    })
  },

  // 查看数据详情
  viewDataDetail(e) {
    const type = e.currentTarget.dataset.type
    wx.switchTab({
      url: '/pages/data-center/index'
    })
  },

  // 分享应用
  shareApp() {
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    })

    wx.showToast({
      title: '请点击右上角分享',
      icon: 'none'
    })
  },

  // 备份数据
  backupData() {
    wx.showLoading({
      title: '备份中...'
    })

    setTimeout(() => {
      wx.hideLoading()
      wx.showToast({
        title: '备份成功',
        icon: 'success'
      })
    }, 2000)
  },

  // 评价应用
  rateApp() {
    wx.showModal({
      title: '应用评价',
      content: '如果您觉得这个应用对您有帮助，请给我们一个好评吧！',
      confirmText: '去评价',
      success: (res) => {
        if (res.confirm) {
          wx.showToast({
            title: '感谢您的支持',
            icon: 'success'
          })
        }
      }
    })
  },

  // 检查更新
  checkUpdate() {
    wx.showLoading({
      title: '检查中...'
    })

    setTimeout(() => {
      wx.hideLoading()
      wx.showToast({
        title: '已是最新版本',
        icon: 'success'
      })
    }, 1500)
  },

  // 登录
  async login() {
    try {
      wx.showLoading({
        title: '登录中...'
      })

      const result = await LoginApi.login()
      wx.hideLoading()

      if (result.success) {
        this.setData({
          isLoggedIn: true,
          userInfo: result.data.user
        })

        wx.showToast({
          title: '登录成功',
          icon: 'success'
        })

        // 刷新页面数据
        this.loadData()
      } else {
        wx.showToast({
          title: result.error || '登录失败',
          icon: 'error'
        })
      }
    } catch (error) {
      wx.hideLoading()
      console.error('登录失败:', error)
      wx.showToast({
        title: '登录失败',
        icon: 'error'
      })
    }
  },

  // 退出登录
  logout() {
    wx.showModal({
      title: '确认退出',
      content: '退出登录后，数据将无法同步，确定要退出吗？',
      success: (res) => {
        if (res.confirm) {
          const app = getApp()
          const result = app.logout()

          if (result.success) {
            this.setData({
              isLoggedIn: false,
              userInfo: {}
            })

            wx.showToast({
              title: '已退出登录',
              icon: 'success'
            })

            // 跳转到登录页
            setTimeout(() => {
              wx.redirectTo({
                url: '/pages/login/index'
              })
            }, 1500)
          }
        }
      }
    })
  },

  // 跳转到登录页
  goToLogin() {
    wx.navigateTo({
      url: '/pages/login/index'
    })
  },

  // 头像变更回调
  onAvatarChange(e) {
    const { avatarUrl, success } = e.detail

    if (success) {
      // 更新本地用户信息
      const userInfo = { ...this.data.userInfo }
      userInfo.avatarUrl = avatarUrl

      this.setData({ userInfo })

      // 更新全局用户信息
      const app = getApp()
      if (app.globalData.userInfo) {
        app.globalData.userInfo.avatarUrl = avatarUrl
        wx.setStorageSync('kaoba_user_info', app.globalData.userInfo)
      }

      console.log('头像更新成功:', avatarUrl)
    }
  },

  // 编辑弹窗中的头像变更回调
  onEditAvatarChange(e) {
    const { avatarUrl, success } = e.detail

    if (success) {
      // 更新编辑表单中的头像
      const editUserInfo = { ...this.data.editUserInfo }
      editUserInfo.avatarUrl = avatarUrl

      this.setData({ editUserInfo })

      console.log('编辑头像更新成功:', avatarUrl)
    }
  }
})