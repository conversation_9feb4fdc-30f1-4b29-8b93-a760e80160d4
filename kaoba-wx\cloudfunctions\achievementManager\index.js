// 成就系统管理云函数
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

exports.main = async (event, context) => {
  const { action, data } = event
  const { OPENID: userId } = cloud.getWXContext()

  console.log('achievementManager云函数调用:', { action, userId })

  try {
    switch (action) {
      case 'getUserAchievements':
        return await getUserAchievements(userId, data)
      case 'checkAchievements':
        return await checkAchievements(userId, data)
      case 'unlockAchievement':
        return await unlockAchievement(userId, data)
      case 'getAchievementStats':
        return await getAchievementStats(userId, data)
      case 'initAchievements':
        return await initAchievements()
      default:
        return { success: false, error: '未知操作' }
    }
  } catch (error) {
    console.error('achievementManager云函数执行失败:', error)
    return { success: false, error: error.message }
  }
}

// 获取用户成就
async function getUserAchievements(userId, params) {
  const { category = null } = params || {}
  
  try {
    // 获取所有成就定义
    const achievementsQuery = db.collection('achievements')
    if (category) {
      achievementsQuery.where({ category: category, isActive: true })
    } else {
      achievementsQuery.where({ isActive: true })
    }
    
    const achievementsResult = await achievementsQuery.get()
    
    // 获取用户已解锁的成就
    const userAchievementsResult = await db.collection('user_achievements')
      .where({ userId: userId })
      .get()

    const unlockedMap = {}
    userAchievementsResult.data.forEach(ua => {
      unlockedMap[ua.achievementId] = ua
    })

    // 合并数据
    const achievements = achievementsResult.data.map(achievement => ({
      ...achievement,
      unlocked: !!unlockedMap[achievement._id],
      unlockedTime: unlockedMap[achievement._id]?.unlockedTime || null,
      progress: unlockedMap[achievement._id]?.progress || 0,
      isNew: unlockedMap[achievement._id]?.isNew || false
    }))

    return { success: true, data: achievements }
  } catch (error) {
    console.error('获取用户成就失败:', error)
    return { success: false, error: '获取成就失败' }
  }
}

// 检查成就解锁条件
async function checkAchievements(userId, params) {
  const { triggerType, value } = params || {}
  
  try {
    // 获取所有未解锁的成就
    const achievementsResult = await db.collection('achievements')
      .where({ isActive: true })
      .get()

    const userAchievementsResult = await db.collection('user_achievements')
      .where({ userId: userId })
      .get()

    const unlockedIds = userAchievementsResult.data.map(ua => ua.achievementId)
    const unlockedAchievements = []

    // 获取用户统计数据
    const statsResult = await db.collection('daily_stats')
      .where({ userId: userId })
      .orderBy('date', 'desc')
      .limit(30)
      .get()

    const totalStats = statsResult.data.reduce((acc, day) => {
      acc.totalStudyTime += day.studyTime || 0
      acc.totalTasks += day.completedTasks || 0
      acc.totalPomodoros += day.pomodoroSessions || 0
      return acc
    }, { totalStudyTime: 0, totalTasks: 0, totalPomodoros: 0 })

    // 检查每个未解锁的成就
    for (const achievement of achievementsResult.data) {
      if (unlockedIds.includes(achievement._id)) continue

      let shouldUnlock = false
      const condition = achievement.condition

      switch (condition.type) {
        case 'task_count':
          shouldUnlock = totalStats.totalTasks >= condition.target
          break
        case 'study_time':
          shouldUnlock = totalStats.totalStudyTime >= condition.target * 3600 // 转换为秒
          break
        case 'pomodoro_count':
          shouldUnlock = totalStats.totalPomodoros >= condition.target
          break
        case 'streak_days':
          // 计算连续学习天数
          const streakDays = calculateStreakDays(statsResult.data)
          shouldUnlock = streakDays >= condition.target
          break
      }

      if (shouldUnlock) {
        await unlockAchievementInternal(userId, achievement._id)
        unlockedAchievements.push(achievement)
      }
    }

    return { success: true, data: { unlockedAchievements } }
  } catch (error) {
    console.error('检查成就失败:', error)
    return { success: false, error: '检查成就失败' }
  }
}

// 解锁成就
async function unlockAchievement(userId, params) {
  const { achievementId } = params || {}
  
  return await unlockAchievementInternal(userId, achievementId)
}

// 内部解锁成就方法
async function unlockAchievementInternal(userId, achievementId) {
  try {
    // 检查是否已解锁
    const existingResult = await db.collection('user_achievements')
      .where({
        userId: userId,
        achievementId: achievementId
      })
      .get()

    if (existingResult.data.length > 0) {
      return { success: false, error: '成就已解锁' }
    }

    // 创建解锁记录
    const unlockRecord = {
      userId: userId,
      achievementId: achievementId,
      unlockedTime: new Date().toISOString(),
      progress: 100,
      isNew: true,
      createTime: new Date().toISOString()
    }

    await db.collection('user_achievements').add({
      data: unlockRecord
    })

    return { success: true, data: unlockRecord }
  } catch (error) {
    console.error('解锁成就失败:', error)
    return { success: false, error: '解锁成就失败' }
  }
}

// 获取成就统计
async function getAchievementStats(userId, params) {
  try {
    // 获取所有成就分类统计
    const achievementsResult = await db.collection('achievements')
      .where({ isActive: true })
      .get()

    const userAchievementsResult = await db.collection('user_achievements')
      .where({ userId: userId })
      .get()

    const unlockedIds = userAchievementsResult.data.map(ua => ua.achievementId)
    
    // 按分类统计
    const categoryStats = {}
    achievementsResult.data.forEach(achievement => {
      const category = achievement.category
      if (!categoryStats[category]) {
        categoryStats[category] = { total: 0, unlocked: 0 }
      }
      categoryStats[category].total++
      if (unlockedIds.includes(achievement._id)) {
        categoryStats[category].unlocked++
      }
    })

    // 格式化返回数据
    const stats = Object.keys(categoryStats).map(category => ({
      category: category,
      name: getCategoryName(category),
      unlocked: categoryStats[category].unlocked,
      total: categoryStats[category].total,
      progress: Math.round((categoryStats[category].unlocked / categoryStats[category].total) * 100)
    }))

    return { success: true, data: stats }
  } catch (error) {
    console.error('获取成就统计失败:', error)
    return { success: false, error: '获取统计失败' }
  }
}

// 初始化成就数据
async function initAchievements() {
  try {
    const achievements = [
      // 学习成就
      {
        name: '初学者',
        description: '完成第一个复习任务',
        icon: '🌱',
        color: '#52C41A',
        category: 'study',
        condition: { type: 'task_count', target: 1, timeframe: 'total' },
        points: 10,
        isActive: true
      },
      {
        name: '勤奋学者',
        description: '累计完成50个复习任务',
        icon: '📚',
        color: '#1890FF',
        category: 'study',
        condition: { type: 'task_count', target: 50, timeframe: 'total' },
        points: 100,
        isActive: true
      },
      // 专注成就
      {
        name: '专注新手',
        description: '完成第一个番茄钟',
        icon: '🍅',
        color: '#FA8C16',
        category: 'focus',
        condition: { type: 'pomodoro_count', target: 1, timeframe: 'total' },
        points: 10,
        isActive: true
      },
      {
        name: '专注大师',
        description: '累计完成100个番茄钟',
        icon: '🎯',
        color: '#722ED1',
        category: 'focus',
        condition: { type: 'pomodoro_count', target: 100, timeframe: 'total' },
        points: 200,
        isActive: true
      },
      // 习惯成就
      {
        name: '坚持不懈',
        description: '连续学习7天',
        icon: '🔥',
        color: '#FF4D4F',
        category: 'habit',
        condition: { type: 'streak_days', target: 7, timeframe: 'streak' },
        points: 50,
        isActive: true
      }
    ]

    // 检查并插入成就数据
    for (const achievement of achievements) {
      const existing = await db.collection('achievements')
        .where({ name: achievement.name })
        .get()

      if (existing.data.length === 0) {
        await db.collection('achievements').add({
          data: {
            ...achievement,
            createTime: new Date().toISOString()
          }
        })
      }
    }

    return { success: true, message: '成就数据初始化完成' }
  } catch (error) {
    console.error('初始化成就失败:', error)
    return { success: false, error: '初始化失败' }
  }
}

// 计算连续学习天数
function calculateStreakDays(dailyStats) {
  if (!dailyStats || dailyStats.length === 0) return 0
  
  let streak = 0
  const today = new Date().toISOString().split('T')[0]
  
  // 从今天开始往前计算连续天数
  for (let i = 0; i < dailyStats.length; i++) {
    const stat = dailyStats[i]
    if (stat.studyTime > 0) {
      streak++
    } else {
      break
    }
  }
  
  return streak
}

// 获取分类名称
function getCategoryName(category) {
  const names = {
    'study': '学习成就',
    'focus': '专注成就',
    'exam': '考试成就',
    'habit': '习惯成就',
    'special': '特殊成就'
  }
  return names[category] || category
}
