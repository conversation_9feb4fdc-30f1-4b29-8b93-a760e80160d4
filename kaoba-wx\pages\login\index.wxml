<!--pages/login/index.wxml-->
<view class="container">
  <!-- 登录成功状态 -->
  <view class="login-success" wx:if="{{hasUserInfo}}">
    <view class="avatar-container">
      <image class="avatar" src="{{userInfo.avatarUrl || '/images/default-avatar.png'}}" mode="aspectFill"></image>
    </view>
    <view class="welcome-text">
      <text class="greeting">欢迎回来</text>
      <text class="nickname">{{userInfo.nickName}}</text>
    </view>
    <view class="loading-indicator">
      <text>正在进入应用...</text>
    </view>
  </view>

  <!-- 自动登录中 -->
  <view class="auto-login" wx:elif="{{loginMethod === 'auto' && loading}}">
    <view class="logo-container">
      <image class="logo" src="/images/logo.png" mode="aspectFit"></image>
      <text class="app-name">考霸</text>
      <text class="app-slogan">你的备考助手</text>
    </view>
    
    <view class="loading-container">
      <view class="loading-spinner"></view>
      <text class="loading-text">正在登录...</text>
    </view>
  </view>

  <!-- 手动登录界面 -->
  <view class="manual-login" wx:else>
    <view class="logo-container">
      <image class="logo" src="/images/logo.png" mode="aspectFit"></image>
      <text class="app-name">考霸</text>
      <text class="app-slogan">你的备考助手</text>
    </view>

    <view class="login-form">
      <!-- 新版授权方式 -->
      <view class="user-info-section" wx:if="{{canIUse}}">
        <view class="avatar-section">
          <text class="section-title">选择头像</text>
          <button class="avatar-button" open-type="chooseAvatar" bind:chooseavatar="chooseAvatar">
            <image class="avatar-preview" src="{{userInfo.avatarUrl || '/images/default-avatar.png'}}" mode="aspectFill"></image>
            <view class="avatar-overlay">
              <text>点击选择</text>
            </view>
          </button>
        </view>

        <view class="nickname-section">
          <text class="section-title">输入昵称</text>
          <input 
            class="nickname-input" 
            type="nickname" 
            placeholder="请输入您的昵称"
            value="{{userInfo.nickName}}"
            bindinput="onNicknameInput"
            maxlength="20"
          />
        </view>

        <button 
          class="login-button primary" 
          bindtap="manualLogin"
          disabled="{{loading}}"
        >
          {{loading ? '登录中...' : '开始使用'}}
        </button>
      </view>

      <!-- 旧版授权方式 -->
      <view class="old-auth-section" wx:else>
        <button 
          class="login-button primary" 
          open-type="getUserInfo" 
          bindgetuserinfo="getUserInfo"
          disabled="{{loading}}"
        >
          {{loading ? '登录中...' : '微信授权登录'}}
        </button>
      </view>

      <!-- 其他选项 -->
      <view class="login-options">
        <button class="option-button" bindtap="skipLogin">游客模式</button>
        <button class="option-button" bindtap="retryLogin" wx:if="{{!loading}}">重新登录</button>
      </view>
    </view>

    <!-- 登录说明 -->
    <view class="login-tips">
      <text class="tip-text">• 登录后可以同步学习数据</text>
      <text class="tip-text">• 支持多设备数据同步</text>
      <text class="tip-text">• 我们不会获取您的敏感信息</text>
    </view>
  </view>

  <!-- 版本信息 -->
  <view class="version-info">
    <text>考霸 v1.0.0</text>
  </view>
</view>
