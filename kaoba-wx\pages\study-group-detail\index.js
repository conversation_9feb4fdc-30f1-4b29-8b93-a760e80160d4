// pages/study-group-detail/index.js
const SmartApi = require('../../utils/smartApi')

Page({
  data: {
    groupId: '',
    groupInfo: null,
    members: [],
    shares: [],
    activities: [],
    loading: true,
    currentUserId: '',
    
    // Tab切换
    activeTab: 'progress', // progress, shares, activities
    
    // 成员进度数据
    memberProgress: []
  },

  async onLoad(options) {
    const { groupId } = options
    if (!groupId) {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      })
      wx.navigateBack()
      return
    }

    this.setData({ groupId })
    
    // 获取当前用户ID
    const app = getApp()
    this.setData({ currentUserId: app.globalData.openid })
    
    await this.loadGroupDetail()
  },

  onShow() {
    // 页面显示时刷新数据
    if (this.data.groupId) {
      this.loadGroupDetail()
    }
  },

  // 加载小组详情
  async loadGroupDetail() {
    this.setData({ loading: true })

    try {
      console.log('开始加载小组详情，groupId:', this.data.groupId)
      const result = await SmartApi.getStudyGroupDetail(this.data.groupId)

      console.log('小组详情API返回结果:', result)

      if (result.success && result.data) {
        const { group, shares, activities } = result.data

        console.log('小组信息:', group)
        console.log('分享计划:', shares)
        console.log('小组动态:', activities)

        this.setData({
          groupInfo: group,
          members: group.members || [],
          shares: shares || [],
          activities: activities || []
        })

        // 加载成员进度
        await this.loadMemberProgress()
      } else {
        console.error('API返回错误:', result.error)
        wx.showModal({
          title: '加载失败',
          content: `错误信息：${result.error || '未知错误'}\n\n小组ID：${this.data.groupId}`,
          showCancel: false
        })
      }
    } catch (error) {
      console.error('加载小组详情失败:', error)
      wx.showModal({
        title: '加载失败',
        content: `网络错误：${error.message}\n\n请检查网络连接或联系开发者`,
        showCancel: false
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  // 加载成员进度
  async loadMemberProgress() {
    const { members, groupInfo } = this.data
    if (!members || !groupInfo) return

    try {
      // 获取小组关联考试的所有任务
      const tasksResult = await SmartApi.getTasks({ examId: groupInfo.examId }, 100, 0)
      
      if (tasksResult.success && tasksResult.data) {
        const allTasks = tasksResult.data
        
        // 计算每个成员的进度
        const memberProgress = members.map(member => {
          const memberTasks = allTasks.filter(task => task.userId === member.userId)
          const completedTasks = memberTasks.filter(task => task.completed)
          
          return {
            userId: member.userId,
            userInfo: member.userInfo,
            totalTasks: memberTasks.length,
            completedTasks: completedTasks.length,
            progress: memberTasks.length > 0 ? Math.round((completedTasks.length / memberTasks.length) * 100) : 0,
            recentTasks: memberTasks.slice(0, 3) // 最近3个任务
          }
        })

        this.setData({ memberProgress })
      }
    } catch (error) {
      console.error('加载成员进度失败:', error)
    }
  },

  // 切换Tab
  onTabChange(e) {
    const { tab } = e.currentTarget.dataset
    this.setData({ activeTab: tab })
  },

  // 邀请新成员
  onInviteMembers() {
    const { groupInfo } = this.data
    
    if (groupInfo.currentMembers >= groupInfo.maxMembers) {
      wx.showToast({
        title: '小组人数已满',
        icon: 'none'
      })
      return
    }

    wx.showModal({
      title: '邀请新成员',
      content: `邀请码：${groupInfo.inviteCode}\n\n分享给朋友加入小组吧！`,
      showCancel: true,
      cancelText: '复制邀请码',
      confirmText: '分享邀请',
      success: (res) => {
        if (res.cancel) {
          // 复制邀请码
          wx.setClipboardData({
            data: groupInfo.inviteCode,
            success: () => {
              wx.showToast({
                title: '邀请码已复制',
                icon: 'success'
              })
            }
          })
        } else if (res.confirm) {
          // 分享邀请
          this.shareInvite()
        }
      }
    })
  },

  // 分享邀请
  shareInvite() {
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    })
  },

  // 分享给朋友
  onShareAppMessage() {
    const { groupInfo } = this.data
    return {
      title: `一起备考${groupInfo?.examName || '考试'}吧！`,
      path: `/pages/join-study-group/index?inviteCode=${groupInfo?.inviteCode}`,
      imageUrl: '/images/share-study-group.png'
    }
  },

  // 退出小组
  onLeaveGroup() {
    wx.showModal({
      title: '确认退出',
      content: '退出后将无法查看小组信息，确定要退出吗？',
      success: async (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '退出中...',
            mask: true
          })

          try {
            const result = await SmartApi.leaveStudyGroup(this.data.groupId)
            
            wx.hideLoading()
            
            if (result.success) {
              wx.showToast({
                title: '已退出小组',
                icon: 'success'
              })
              
              setTimeout(() => {
                wx.navigateBack()
              }, 1500)
            } else {
              wx.showToast({
                title: result.error || '退出失败',
                icon: 'none'
              })
            }
          } catch (error) {
            wx.hideLoading()
            wx.showToast({
              title: '退出失败，请重试',
              icon: 'none'
            })
          }
        }
      }
    })
  },

  // 分享计划
  onSharePlan() {
    const { groupInfo } = this.data
    wx.navigateTo({
      url: `/pages/share-plan/index?groupId=${this.data.groupId}&examId=${groupInfo.examId}`
    })
  },

  // 查看成员详情
  onViewMember(e) {
    const { userId } = e.currentTarget.dataset
    // 可以跳转到成员的个人页面或任务列表
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    })
  },

  // 点赞计划
  async onLikePlan(e) {
    const { shareId, index } = e.currentTarget.dataset
    
    try {
      const result = await SmartApi.likeGroupPlan(shareId)
      
      if (result.success) {
        // 更新本地数据
        const shares = this.data.shares
        shares[index].liked = result.data.liked
        shares[index].likeCount = result.data.likeCount
        
        this.setData({ shares })
        
        wx.showToast({
          title: result.data.liked ? '已点赞' : '已取消点赞',
          icon: 'success'
        })
      }
    } catch (error) {
      console.error('点赞失败:', error)
      wx.showToast({
        title: '操作失败',
        icon: 'none'
      })
    }
  },

  // 复制计划
  async onCopyPlan(e) {
    const { shareId } = e.currentTarget.dataset
    
    wx.showModal({
      title: '复制计划',
      content: '确定要复制这个复习计划吗？',
      success: async (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '复制中...',
            mask: true
          })

          try {
            const result = await SmartApi.copyGroupPlan(shareId)
            
            wx.hideLoading()
            
            if (result.success) {
              wx.showToast({
                title: '复制成功！',
                icon: 'success'
              })
              
              // 刷新数据
              await this.loadGroupDetail()
            } else {
              wx.showToast({
                title: result.error || '复制失败',
                icon: 'none'
              })
            }
          } catch (error) {
            wx.hideLoading()
            wx.showToast({
              title: '复制失败，请重试',
              icon: 'none'
            })
          }
        }
      }
    })
  }
})
