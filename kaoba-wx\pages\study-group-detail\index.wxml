<!-- pages/study-group-detail/index.wxml -->
<view class="container" wx:if="{{!loading}}">
  <!-- 小组信息头部 -->
  <view class="group-header">
    <view class="group-info">
      <text class="group-name">{{groupInfo.groupName}}</text>
      <text class="exam-name">{{groupInfo.examName}}</text>
      <text class="member-count">{{groupInfo.currentMembers}}/{{groupInfo.maxMembers}}人</text>
    </view>
    <view class="group-actions">
      <button class="btn btn-secondary" bindtap="onInviteMembers" wx:if="{{groupInfo.currentMembers < groupInfo.maxMembers}}">
        邀请成员
      </button>
      <button class="btn btn-primary" bindtap="onSharePlan">分享计划</button>
    </view>
  </view>

  <!-- Tab导航 -->
  <view class="tab-nav">
    <view 
      class="tab-item {{activeTab === 'progress' ? 'active' : ''}}"
      bindtap="onTabChange"
      data-tab="progress"
    >
      <text>成员进度</text>
    </view>
    <view 
      class="tab-item {{activeTab === 'shares' ? 'active' : ''}}"
      bindtap="onTabChange"
      data-tab="shares"
    >
      <text>共享计划</text>
    </view>
    <view 
      class="tab-item {{activeTab === 'activities' ? 'active' : ''}}"
      bindtap="onTabChange"
      data-tab="activities"
    >
      <text>小组动态</text>
    </view>
  </view>

  <!-- 成员进度 -->
  <view class="tab-content" wx:if="{{activeTab === 'progress'}}">
    <view class="member-list">
      <view 
        class="member-item" 
        wx:for="{{memberProgress}}" 
        wx:key="userId"
        bindtap="onViewMember"
        data-user-id="{{item.userId}}"
      >
        <view class="member-avatar">
          <image src="{{item.userInfo.avatarUrl || '/images/default-avatar.png'}}" class="avatar-img" />
        </view>
        <view class="member-info">
          <text class="member-name">{{item.userInfo.nickName}}</text>
          <text class="member-stats">{{item.completedTasks}}/{{item.totalTasks}} 已完成</text>
        </view>
        <view class="progress-info">
          <view class="progress-bar">
            <view class="progress-fill" style="width: {{item.progress}}%"></view>
          </view>
          <text class="progress-text">{{item.progress}}%</text>
        </view>
      </view>
    </view>
    
    <view class="empty-state" wx:if="{{memberProgress.length === 0}}">
      <text class="empty-icon">👥</text>
      <text class="empty-text">暂无成员进度数据</text>
    </view>
  </view>

  <!-- 共享计划 -->
  <view class="tab-content" wx:if="{{activeTab === 'shares'}}">
    <view class="share-list">
      <view class="share-item" wx:for="{{shares}}" wx:key="_id" wx:for-index="index">
        <view class="share-header">
          <view class="sharer-info">
            <image src="{{item.sharerInfo.avatarUrl || '/images/default-avatar.png'}}" class="sharer-avatar" />
            <view class="sharer-details">
              <text class="sharer-name">{{item.sharerInfo.nickName}}</text>
              <text class="share-time">{{item.shareTime}}</text>
            </view>
          </view>
        </view>
        
        <view class="plan-content">
          <text class="plan-title">{{item.planData.title}}</text>
          <text class="plan-desc" wx:if="{{item.planData.description}}">{{item.planData.description}}</text>
          <view class="plan-stats">
            <text class="stat-item">{{item.planData.totalTasks || 0}}个任务</text>
            <text class="stat-item">预计{{item.planData.estimatedDays || 0}}天</text>
          </view>
        </view>
        
        <view class="share-actions">
          <button 
            class="action-btn like-btn {{item.liked ? 'liked' : ''}}"
            bindtap="onLikePlan"
            data-share-id="{{item._id}}"
            data-index="{{index}}"
          >
            <text class="action-icon">👍</text>
            <text class="action-text">{{item.likeCount || 0}}</text>
          </button>
          <button 
            class="action-btn copy-btn"
            bindtap="onCopyPlan"
            data-share-id="{{item._id}}"
          >
            <text class="action-icon">📋</text>
            <text class="action-text">复制</text>
          </button>
        </view>
      </view>
    </view>
    
    <view class="empty-state" wx:if="{{shares.length === 0}}">
      <text class="empty-icon">📋</text>
      <text class="empty-text">还没有共享计划</text>
      <button class="btn btn-primary" bindtap="onSharePlan">分享第一个计划</button>
    </view>
  </view>

  <!-- 小组动态 -->
  <view class="tab-content" wx:if="{{activeTab === 'activities'}}">
    <view class="activity-list">
      <view class="activity-item" wx:for="{{activities}}" wx:key="_id">
        <view class="activity-avatar">
          <image src="{{item.userInfo.avatarUrl || '/images/default-avatar.png'}}" class="avatar-img" />
        </view>
        <view class="activity-content">
          <text class="activity-text">
            <text class="user-name">{{item.userInfo.nickName}}</text>
            <text wx:if="{{item.activityType === 'join_group'}}">加入了小组</text>
            <text wx:if="{{item.activityType === 'plan_share'}}">分享了计划《{{item.activityData.planTitle}}》</text>
            <text wx:if="{{item.activityType === 'like_plan'}}">点赞了计划《{{item.activityData.planTitle}}》</text>
            <text wx:if="{{item.activityType === 'copy_plan'}}">复制了计划《{{item.activityData.planTitle}}》</text>
            <text wx:if="{{item.activityType === 'task_complete'}}">完成了任务《{{item.activityData.taskTitle}}》</text>
          </text>
          <text class="activity-time">{{item.createTime}}</text>
        </view>
      </view>
    </view>
    
    <view class="empty-state" wx:if="{{activities.length === 0}}">
      <text class="empty-icon">📝</text>
      <text class="empty-text">暂无小组动态</text>
    </view>
  </view>

  <!-- 底部操作 -->
  <view class="bottom-actions">
    <button class="btn btn-secondary" bindtap="onTestDatabase">测试数据库</button>
    <button class="btn btn-danger" bindtap="onLeaveGroup">退出小组</button>
  </view>
</view>

<!-- 加载状态 -->
<view class="loading-container" wx:if="{{loading}}">
  <view class="loading-content">
    <text class="loading-text">加载中...</text>
  </view>
</view>
