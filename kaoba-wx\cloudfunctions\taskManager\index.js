// 任务管理云函数
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()
const _ = db.command

// 根据openid获取用户ID
async function getUserId(openid) {
  try {
    const userResult = await db.collection('users')
      .where({ openid: openid })
      .get()

    if (userResult.data.length > 0) {
      return userResult.data[0]._id
    }
    return null
  } catch (error) {
    console.error('获取用户ID失败:', error)
    return null
  }
}

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const { action, data } = event

  try {
    // 获取用户ID
    const userId = await getUserId(wxContext.OPENID)
    if (!userId) {
      return { success: false, error: '用户未登录或不存在' }
    }

    switch (action) {
      case 'getTasks':
        return await getTasks(userId, data)
      case 'getTaskById':
        return await getTaskById(userId, data)
      case 'addTask':
        return await addTask(userId, data)
      case 'updateTask':
        return await updateTask(userId, data)
      case 'deleteTask':
        return await deleteTask(userId, data)
      case 'completeTask':
        return await completeTask(userId, data)
      case 'getTaskStats':
        return await getTaskStats(userId, data)
      default:
        return { success: false, error: '未知操作' }
    }
  } catch (error) {
    console.error('任务管理云函数错误:', error)
    return { success: false, error: error.message }
  }
}

// 获取任务列表
async function getTasks(userId, params) {
  const { filter, limit = 20, skip = 0 } = params || {}

  let query = db.collection('tasks').where({
    userId: userId
  })
  
  if (filter) {
    if (filter.completed !== undefined) {
      query = query.where({ completed: filter.completed })
    }
    if (filter.priority) {
      query = query.where({ priority: filter.priority })
    }
    if (filter.subject) {
      query = query.where({ subject: filter.subject })
    }
    if (filter.dateRange) {
      const { start, end } = filter.dateRange
      query = query.where({
        dueDate: _.gte(new Date(start)).and(_.lte(new Date(end)))
      })
    }
  }
  
  const result = await query
    .orderBy('dueDate', 'asc')
    .orderBy('priority', 'desc')
    .skip(skip)
    .limit(limit)
    .get()
  
  return { success: true, data: result.data, total: result.data.length }
}

// 根据ID获取单个任务
async function getTaskById(userId, params) {
  const { taskId } = params || {}

  if (!taskId) {
    return { success: false, error: '任务ID不能为空' }
  }

  try {
    const result = await db.collection('tasks').doc(taskId).get()

    if (!result.data) {
      return { success: false, error: '任务不存在' }
    }

    // 检查任务是否属于当前用户
    if (result.data.userId !== userId) {
      return { success: false, error: '无权访问该任务' }
    }

    return { success: true, data: result.data }
  } catch (error) {
    console.error('获取任务详情失败:', error)
    return { success: false, error: '获取任务详情失败' }
  }
}

// 添加任务
async function addTask(userId, taskData) {
  const task = {
    ...taskData,
    userId: userId,
    completed: false,
    createTime: new Date(),
    updateTime: new Date()
  }

  const result = await db.collection('tasks').add({ data: task })
  return { success: true, data: { _id: result._id, ...task } }
}

// 更新任务
async function updateTask(userId, { taskId, updates }) {
  const updateData = {
    ...updates,
    updateTime: new Date()
  }

  const result = await db.collection('tasks')
    .where({ _id: taskId, userId: userId })
    .update({ data: updateData })

  return { success: true, data: result }
}

// 删除任务
async function deleteTask(userId, { taskId }) {
  const result = await db.collection('tasks')
    .where({ _id: taskId, userId: userId })
    .remove()

  return { success: true, data: result }
}

// 完成任务
async function completeTask(userId, { taskId, completed = true }) {
  const updateData = {
    completed,
    updateTime: new Date()
  }

  if (completed) {
    updateData.completedTime = new Date()
  }

  const result = await db.collection('tasks')
    .where({ _id: taskId, userId: userId })
    .update({ data: updateData })

  return { success: true, data: result }
}

// 获取任务统计
async function getTaskStats(userId, params) {
  const { dateRange } = params || {}

  let query = db.collection('tasks').where({
    userId: userId
  })
  
  if (dateRange) {
    const { start, end } = dateRange
    query = query.where({
      createTime: _.gte(new Date(start)).and(_.lte(new Date(end)))
    })
  }
  
  const allTasks = await query.get()
  const tasks = allTasks.data
  
  const stats = {
    total: tasks.length,
    completed: tasks.filter(t => t.completed).length,
    pending: tasks.filter(t => !t.completed).length,
    overdue: tasks.filter(t => !t.completed && new Date(t.dueDate) < new Date()).length,
    byPriority: {
      high: tasks.filter(t => t.priority === 'high').length,
      medium: tasks.filter(t => t.priority === 'medium').length,
      low: tasks.filter(t => t.priority === 'low').length
    },
    bySubject: {}
  }
  
  // 按科目统计
  tasks.forEach(task => {
    if (task.subject) {
      stats.bySubject[task.subject] = (stats.bySubject[task.subject] || 0) + 1
    }
  })
  
  return { success: true, data: stats }
}
