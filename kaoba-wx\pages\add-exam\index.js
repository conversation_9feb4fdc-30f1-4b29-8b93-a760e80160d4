// pages/add-exam/index.js
const SmartApi = require('../../utils/smartApi')

Page({
  data: {
    examForm: {
      title: '',
      subject: '',
      date: '',
      time: '',
      location: '',
      importance: 'medium',
      type: 'final',
      notes: ''
    },

    reminderOptions: [
      { value: '1day', label: '考试前1天', checked: true },
      { value: '3days', label: '考试前3天', checked: true },
      { value: '1week', label: '考试前1周', checked: false },
      { value: '2weeks', label: '考试前2周', checked: false }
    ],

    todayDate: '',
    canSave: false,
    showSuccessModal: false,
    selectedReminders: ['1day', '3days']
  },

  onLoad(options) {
    this.initPage()
  },

  // 初始化页面
  initPage() {
    const today = new Date()
    const todayDate = this.formatDate(today)
    this.setData({ todayDate })
    this.validateForm()
  },

  // 格式化日期
  formatDate(date) {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
  },

  // 输入事件处理
  onTitleInput(e) {
    this.setData({
      'examForm.title': e.detail.value
    })
    this.validateForm()
  },

  onSubjectInput(e) {
    this.setData({
      'examForm.subject': e.detail.value
    })
    this.validateForm()
  },

  onLocationInput(e) {
    this.setData({
      'examForm.location': e.detail.value
    })
  },

  onNotesInput(e) {
    this.setData({
      'examForm.notes': e.detail.value
    })
  },

  // 日期时间选择
  onDateChange(e) {
    this.setData({
      'examForm.date': e.detail.value
    })
    this.validateForm()
  },

  onTimeChange(e) {
    this.setData({
      'examForm.time': e.detail.value
    })
    this.validateForm()
  },

  // 重要程度选择
  selectImportance(e) {
    const level = e.currentTarget.dataset.level
    this.setData({
      'examForm.importance': level
    })
  },

  // 考试类型选择
  selectType(e) {
    const type = e.currentTarget.dataset.type
    this.setData({
      'examForm.type': type
    })
  },

  // 提醒设置
  onReminderChange(e) {
    const value = e.currentTarget.dataset.value
    const checked = e.detail.value.length > 0

    const reminderOptions = this.data.reminderOptions.map(item => {
      if (item.value === value) {
        return { ...item, checked }
      }
      return item
    })

    const selectedReminders = reminderOptions
      .filter(item => item.checked)
      .map(item => item.value)

    this.setData({
      reminderOptions,
      selectedReminders
    })
  },

  // 表单验证
  validateForm() {
    const { title, subject, date, time } = this.data.examForm
    const canSave = title.trim() && subject.trim() && date && time
    this.setData({ canSave })
  },

  // 保存考试
  async saveExam() {
    if (!this.data.canSave) {
      wx.showToast({
        title: '请填写必填项',
        icon: 'none'
      })
      return
    }

    const examData = {
      title: this.data.examForm.title,
      subject: this.data.examForm.subject,
      examDate: this.data.examForm.examDate,
      examTime: this.data.examForm.examTime,
      location: this.data.examForm.location,
      description: this.data.examForm.description,
      reminderDays: this.data.selectedReminders.length > 0 ? this.data.selectedReminders[0] : 0,
      status: 'upcoming'
    }

    wx.showLoading({
      title: '保存中...',
      mask: true
    })

    try {
      // 使用SmartApi保存到云数据库
      const result = await SmartApi.addExam(examData)

      wx.hideLoading()

      if (result.success) {
        // 显示成功提示
        this.setData({ showSuccessModal: true })

        // 2秒后自动跳转
        setTimeout(() => {
          this.goToExamCenter()
        }, 2000)
      } else {
        wx.showToast({
          title: result.error || '保存失败',
          icon: 'none'
        })
      }

    } catch (error) {
      wx.hideLoading()
      console.error('保存考试失败:', error)
      wx.showToast({
        title: '保存失败，请重试',
        icon: 'none'
      })
    }
  },

  // 取消添加
  cancelAdd() {
    wx.showModal({
      title: '确认取消',
      content: '确定要取消添加考试吗？已填写的内容将丢失。',
      success: (res) => {
        if (res.confirm) {
          wx.navigateBack()
        }
      }
    })
  },

  // 跳转到考试中心
  goToExamCenter() {
    wx.switchTab({
      url: '/pages/exam-center/index'
    })
  }
})