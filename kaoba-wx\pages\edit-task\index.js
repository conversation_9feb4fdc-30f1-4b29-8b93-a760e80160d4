// pages/edit-task/index.js
const SmartApi = require('../../utils/smartApi')

Page({
  data: {
    taskId: '',
    taskForm: {
      title: '',
      description: '',
      examId: '',
      examName: '',
      subject: '',
      priority: 'medium',
      dueDate: '',
      dueTime: '',
      estimatedDuration: '30分钟',
      status: 'pending',
      progress: 0,
      subtasks: []
    },

    subjectOptions: ['数学', '英语', '政治', '专业课', '语文', '物理', '化学', '生物', '历史', '地理'],

    priorityOptions: [
      { value: 'high', label: '高优先级', icon: '🔴', color: '#FF4D4F' },
      { value: 'medium', label: '中优先级', icon: '🟡', color: '#FA8C16' },
      { value: 'low', label: '低优先级', icon: '🟢', color: '#52C41A' }
    ],

    statusOptions: [
      { value: 'pending', label: '进行中', icon: '⏳' },
      { value: 'completed', label: '已完成', icon: '✅' },
      { value: 'paused', label: '暂停', icon: '⏸️' },
      { value: 'cancelled', label: '已取消', icon: '❌' }
    ],

    durationOptions: ['15分钟', '30分钟', '45分钟', '1小时', '1.5小时', '2小时', '3小时', '4小时'],

    examOptions: [],
    showExamModal: false,
    originalTask: {} // 保存原始任务数据
  },

  onLoad(options) {
    const taskId = options.id
    if (taskId) {
      this.setData({ taskId })
      this.loadTaskData(taskId)
    }
    this.loadExamOptions()
  },

  // 加载任务数据
  async loadTaskData(taskId) {
    wx.showLoading({
      title: '加载中...',
      mask: true
    })

    try {
      // 使用SmartApi获取任务详情
      const result = await SmartApi.getTasks({ _id: taskId }, 1, 0)

      wx.hideLoading()

      if (result.success && result.data.length > 0) {
        const task = result.data[0]

        // 保存原始数据用于比较
        this.setData({
          originalTask: JSON.parse(JSON.stringify(task)),
          taskForm: {
            title: task.title || '',
            description: task.description || '',
            examId: task.examId || '',
            examName: task.examName || '',
            subject: task.subject || '',
            priority: task.priority || 'medium',
            dueDate: task.dueDate || '',
            dueTime: task.dueTime || '',
            estimatedDuration: task.estimatedDuration || '30分钟',
            status: task.completed ? 'completed' : 'pending',
            progress: task.progress || 0,
            subtasks: task.subtasks ? task.subtasks.map(item => ({ ...item })) : []
          }
        })
      } else {
        wx.hideLoading()
        wx.showToast({
          title: '任务不存在',
          icon: 'none'
        })
        setTimeout(() => {
          wx.navigateBack()
        }, 1500)
      }
    } catch (error) {
      wx.hideLoading()
      console.error('加载任务数据失败:', error)
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    }
  },

  // 加载考试选项
  loadExamOptions() {
    const examOptions = [
      { id: 'exam_001', name: '2025年考研', date: '2025-12-23' },
      { id: 'exam_002', name: '英语四级', date: '2025-06-15' },
      { id: 'exam_003', name: '计算机二级', date: '2025-03-28' }
    ]

    this.setData({ examOptions })
  },

  // 更新任务标题
  updateTitle(e) {
    this.setData({
      'taskForm.title': e.detail.value
    })
  },

  // 更新任务描述
  updateDescription(e) {
    this.setData({
      'taskForm.description': e.detail.value
    })
  },

  // 选择考试
  selectExam() {
    this.setData({ showExamModal: true })
  },

  // 隐藏考试选择弹窗
  hideExamModal() {
    this.setData({ showExamModal: false })
  },

  // 选择考试选项
  selectExamOption(e) {
    const exam = e.currentTarget.dataset.exam
    this.setData({
      'taskForm.examId': exam.id,
      'taskForm.examName': exam.name,
      showExamModal: false
    })
  },

  // 选择学科
  selectSubject(e) {
    const subject = e.currentTarget.dataset.subject
    this.setData({
      'taskForm.subject': subject
    })
  },

  // 选择优先级
  selectPriority(e) {
    const priority = e.currentTarget.dataset.priority
    this.setData({
      'taskForm.priority': priority
    })
  },

  // 选择状态
  selectStatus(e) {
    const status = e.currentTarget.dataset.status
    this.setData({
      'taskForm.status': status
    })

    // 如果设置为已完成，自动将进度设为100%
    if (status === 'completed') {
      this.setData({
        'taskForm.progress': 100
      })
    }
  },

  // 更新进度
  updateProgress(e) {
    const progress = e.detail.value
    this.setData({
      'taskForm.progress': progress
    })

    // 如果进度达到100%，自动设置为已完成
    if (progress >= 100) {
      this.setData({
        'taskForm.status': 'completed'
      })
    } else if (this.data.taskForm.status === 'completed') {
      // 如果进度小于100%但状态是已完成，改为进行中
      this.setData({
        'taskForm.status': 'pending'
      })
    }
  },

  // 选择截止日期
  selectDueDate(e) {
    this.setData({
      'taskForm.dueDate': e.detail.value
    })
  },

  // 选择截止时间
  selectDueTime(e) {
    this.setData({
      'taskForm.dueTime': e.detail.value
    })
  },

  // 选择预计时长
  selectDuration(e) {
    const duration = e.currentTarget.dataset.duration
    this.setData({
      'taskForm.estimatedDuration': duration
    })
  },

  // 添加子任务
  addSubtask() {
    const subtasks = [...this.data.taskForm.subtasks, { title: '', completed: false }]
    this.setData({
      'taskForm.subtasks': subtasks
    })
  },

  // 切换子任务状态
  toggleSubtask(e) {
    const index = e.currentTarget.dataset.index
    const subtasks = [...this.data.taskForm.subtasks]
    subtasks[index].completed = !subtasks[index].completed

    this.setData({
      'taskForm.subtasks': subtasks
    })

    // 重新计算整体进度
    this.calculateProgress()
  },

  // 更新子任务
  updateSubtask(e) {
    const index = e.currentTarget.dataset.index
    const value = e.detail.value
    const subtasks = [...this.data.taskForm.subtasks]
    subtasks[index].title = value

    this.setData({
      'taskForm.subtasks': subtasks
    })
  },

  // 删除子任务
  removeSubtask(e) {
    const index = e.currentTarget.dataset.index
    const subtasks = [...this.data.taskForm.subtasks]
    subtasks.splice(index, 1)

    this.setData({
      'taskForm.subtasks': subtasks
    })

    // 重新计算整体进度
    this.calculateProgress()
  },

  // 计算任务进度
  calculateProgress() {
    const subtasks = this.data.taskForm.subtasks
    if (subtasks.length === 0) return

    const completedCount = subtasks.filter(item => item.completed).length
    const progress = Math.round((completedCount / subtasks.length) * 100)

    this.setData({
      'taskForm.progress': progress
    })

    // 如果所有子任务完成，设置状态为已完成
    if (progress >= 100) {
      this.setData({
        'taskForm.status': 'completed'
      })
    }
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止事件冒泡
  },

  // 取消编辑
  cancelEdit() {
    // 检查是否有未保存的更改
    if (this.hasUnsavedChanges()) {
      wx.showModal({
        title: '确认取消',
        content: '您有未保存的更改，确定要取消编辑吗？',
        success: (res) => {
          if (res.confirm) {
            wx.navigateBack()
          }
        }
      })
    } else {
      wx.navigateBack()
    }
  },

  // 检查是否有未保存的更改
  hasUnsavedChanges() {
    const current = this.data.taskForm
    const original = this.data.originalTask

    return (
      current.title !== original.title ||
      current.description !== original.description ||
      current.subject !== original.subject ||
      current.priority !== original.priority ||
      current.dueDate !== original.dueDate ||
      current.dueTime !== original.dueTime ||
      current.estimatedDuration !== original.estimatedDuration ||
      current.status !== original.status ||
      current.progress !== original.progress ||
      JSON.stringify(current.subtasks) !== JSON.stringify(original.subtasks)
    )
  },

  // 删除任务
  deleteTask() {
    wx.showModal({
      title: '确认删除',
      content: '确定要删除此任务吗？此操作不可恢复。',
      confirmText: '删除',
      confirmColor: '#FF4D4F',
      success: (res) => {
        if (res.confirm) {
          // 这里应该从存储中删除任务
          wx.showToast({
            title: '任务已删除',
            icon: 'success'
          })

          setTimeout(() => {
            wx.navigateBack()
          }, 1500)
        }
      }
    })
  },

  // 更新任务
  updateTask() {
    const { taskForm } = this.data

    // 验证必填字段
    if (!taskForm.title || !taskForm.title.trim()) {
      wx.showToast({
        title: '请输入任务标题',
        icon: 'none'
      })
      return
    }

    if (!taskForm.subject) {
      wx.showToast({
        title: '请选择学科',
        icon: 'none'
      })
      return
    }

    // 构建更新的任务数据
    const updatedTask = {
      id: this.data.taskId,
      title: taskForm.title.trim(),
      description: taskForm.description.trim(),
      examId: taskForm.examId,
      examName: taskForm.examName,
      subject: taskForm.subject,
      priority: taskForm.priority,
      dueDate: taskForm.dueDate,
      dueTime: taskForm.dueTime,
      estimatedDuration: taskForm.estimatedDuration,
      status: taskForm.status,
      progress: taskForm.progress,
      subtasks: taskForm.subtasks.filter(item => item.title.trim()),
      updateTime: new Date().toISOString()
    }

    // 保存任务（这里应该保存到本地存储或服务器）
    this.saveTask(updatedTask)
  },

  // 保存任务
  async saveTask(taskData) {
    wx.showLoading({
      title: '保存中...',
      mask: true
    })

    try {
      // 使用SmartApi更新任务
      const result = await SmartApi.updateTask(this.data.taskId, {
        title: taskData.title,
        description: taskData.description,
        examId: taskData.examId,
        examName: taskData.examName,
        subject: taskData.subject,
        priority: taskData.priority,
        dueDate: taskData.dueDate,
        dueTime: taskData.dueTime,
        estimatedDuration: taskData.estimatedDuration,
        progress: taskData.progress,
        subtasks: taskData.subtasks
      })

      wx.hideLoading()

      if (result.success) {
        wx.showToast({
          title: '保存成功',
          icon: 'success'
        })

        // 返回上一页
        setTimeout(() => {
          wx.navigateBack()
        }, 1500)
      } else {
        wx.showToast({
          title: result.error || '保存失败',
          icon: 'none'
        })
      }

    } catch (error) {
      wx.hideLoading()
      console.error('保存任务失败:', error)
      wx.showToast({
        title: '保存失败，请重试',
        icon: 'none'
      })
    }
  }
})