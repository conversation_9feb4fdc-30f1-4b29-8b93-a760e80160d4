// pages/pomodoro/index.js
const SmartApi = require('../../utils/smartApi')

Page({
  data: {
    // 模式状态
    focusMode: false,
    studyMode: 'quick', // quick, task

    // 计时器状态
    timerState: 'work', // work, shortBreak, longBreak
    timerStateText: '专注时间',
    sessionTypeText: '专注中',
    sessionIndicator: '●●●●○○○○',
    isRunning: false,
    isPaused: false,

    // 时间设置
    workDuration: 25, // 分钟
    shortBreakDuration: 5,
    longBreakDuration: 15,

    // 当前时间
    currentTime: 25 * 60, // 秒
    totalTime: 25 * 60,
    displayTime: '25:00',
    progressDegree: 0,
    timerColor: '#FF6B6B',

    // 任务关联
    selectedTask: null,
    availableTasks: [],
    taskBreakdown: [],
    currentSession: null,

    // 声音系统
    selectedBgSound: 'silent',
    backgroundSounds: [
      { id: 'silent', name: '静音', icon: '🔇', description: '无背景音' },
      { id: 'rain', name: '雨声', icon: '🌧️', description: '适合深度思考' },
      { id: 'ocean', name: '海浪', icon: '🌊', description: '适合放松学习' },
      { id: 'cafe', name: '咖啡厅', icon: '☕', description: '适合轻松学习' },
      { id: 'forest', name: '森林', icon: '🌲', description: '适合长时间专注' },
      { id: 'whitenoise', name: '白噪音', icon: '🎵', description: '屏蔽外界干扰' }
    ],

    notificationSounds: [
      { id: 'start', name: '开始提示', enabled: true },
      { id: 'pause', name: '暂停提示', enabled: true },
      { id: 'complete', name: '完成提示', enabled: true },
      { id: 'warning', name: '时间警告', enabled: true }
    ],

    volume: 70,
    bgVolume: 70,
    notificationVolume: 80,

    // 弹窗状态
    showTaskModal: false,
    showBreakdownModal: false,
    showSoundModal: false,
    showSettings: false,

    // 计时器和音频
    timer: null,
    backgroundAudio: null,

    // 会话统计
    completedSessions: 0,
    totalSessions: 4,
    todayFocus: '0h'
  },

  async onLoad() {
    // 检查登录状态
    const app = getApp()
    if (!app.globalData.userInfo || !app.globalData.openid) {
      console.log('用户未登录，尝试自动登录...')

      const LoginApi = require('../../utils/loginApi')
      const loginResult = await LoginApi.login()

      if (!loginResult.success) {
        wx.showToast({
          title: '请先登录',
          icon: 'none'
        })
        setTimeout(() => {
          wx.switchTab({
            url: '/pages/home/<USER>'
          })
        }, 1500)
        return
      }
    }

    this.initPage()
    this.loadUserSettings()
    this.loadAvailableTasks()
    this.initAudioSystem()
  },

  onShow() {
    this.refreshTaskList()
    this.updateTodayStats()
    this.updateTabBarState()
  },

  onHide() {
    this.pauseBackgroundAudio()
  },

  onUnload() {
    this.cleanup()
  },

  // 初始化页面
  initPage() {
    this.updateTimerDisplay()
    this.updateSessionInfo()
  },

  // 加载用户设置
  loadUserSettings() {
    try {
      const settings = wx.getStorageSync('pomodoroSettings') || {}

      this.setData({
        workDuration: settings.workDuration || 25,
        shortBreakDuration: settings.shortBreakDuration || 5,
        longBreakDuration: settings.longBreakDuration || 15,
        selectedBgSound: settings.selectedBgSound || 'silent',
        volume: settings.volume || 70,
        bgVolume: settings.bgVolume || 70,
        notificationVolume: settings.notificationVolume || 80,
        notificationSounds: settings.notificationSounds || this.data.notificationSounds
      })

      // 更新当前时间
      const currentTime = this.data.workDuration * 60
      this.setData({
        currentTime,
        totalTime: currentTime,
        displayTime: this.formatTime(currentTime)
      })

    } catch (error) {
      console.error('加载用户设置失败:', error)
    }
  },

  // 保存用户设置
  saveUserSettings() {
    try {
      const settings = {
        workDuration: this.data.workDuration,
        shortBreakDuration: this.data.shortBreakDuration,
        longBreakDuration: this.data.longBreakDuration,
        selectedBgSound: this.data.selectedBgSound,
        volume: this.data.volume,
        bgVolume: this.data.bgVolume,
        notificationVolume: this.data.notificationVolume,
        notificationSounds: this.data.notificationSounds
      }

      wx.setStorageSync('pomodoroSettings', settings)
    } catch (error) {
      console.error('保存用户设置失败:', error)
    }
  },

  // 加载可用任务
  async loadAvailableTasks() {
    try {
      // 使用SmartApi获取未完成的任务
      const result = await SmartApi.getTasks({ completed: false }, 20, 0)

      if (result.success) {
        const availableTasks = result.data.map(task => ({
          id: task._id,
          title: task.title,
          subject: task.subject,
          priority: task.priority,
          estimatedTime: task.estimatedDuration || '30分钟'
        }))

        this.setData({ availableTasks })
      } else {
        console.error('加载任务失败:', result.error)
        this.setData({ availableTasks: [] })
      }
    } catch (error) {
      console.error('加载任务失败:', error)
      this.setData({ availableTasks: [] })
    }
  },

  // 刷新任务列表
  refreshTaskList() {
    this.loadAvailableTasks()
  },

  // 初始化音频系统
  initAudioSystem() {
    try {
      // 创建背景音频管理器
      this.backgroundAudio = wx.createInnerAudioContext()
      this.backgroundAudio.loop = true
      this.backgroundAudio.volume = this.data.bgVolume / 100

      this.backgroundAudio.onError((error) => {
        console.error('背景音频播放失败:', error)
      })

    } catch (error) {
      console.error('初始化音频系统失败:', error)
    }
  },

  // 选择学习模式
  selectStudyMode(e) {
    const mode = e.currentTarget.dataset.mode
    this.setData({ studyMode: mode })

    if (mode === 'quick') {
      // 快速专注模式，清除任务关联
      this.setData({
        selectedTask: null,
        currentSession: {
          title: '快速专注',
          subtitle: '25分钟专注学习'
        }
      })
    } else if (mode === 'task') {
      // 任务专注模式
      if (!this.data.selectedTask) {
        this.showTaskSelector()
      }
    }
  },

  // 显示任务选择器
  showTaskSelector() {
    this.setData({ showTaskModal: true })
  },

  // 隐藏任务选择器
  hideTaskModal() {
    this.setData({ showTaskModal: false })
  },

  // 选择任务
  selectTaskOption(e) {
    const taskData = e.currentTarget.dataset.task

    if (!taskData) {
      // 选择自由专注
      this.setData({
        selectedTask: null,
        studyMode: 'quick',
        currentSession: {
          title: '自由专注',
          subtitle: '25分钟专注学习'
        }
      })
    } else {
      // 选择具体任务
      const task = typeof taskData === 'string' ? JSON.parse(taskData) : taskData
      this.setData({ selectedTask: task })

      // 生成任务拆解建议
      this.generateTaskBreakdown(task)
    }

    this.hideTaskModal()
  },

  // 生成任务拆解建议
  generateTaskBreakdown(task) {
    // 根据任务复杂度生成拆解建议
    const complexity = this.assessTaskComplexity(task)
    const breakdown = this.createBreakdownPlan(task, complexity)

    this.setData({
      taskBreakdown: breakdown,
      selectedTask: {
        ...task,
        breakdown,
        totalPomodoros: breakdown.length,
        completedPomodoros: breakdown.filter(item => item.completed).length
      }
    })

    // 设置当前会话信息
    const currentStage = breakdown.find(item => !item.completed)
    if (currentStage) {
      this.setData({
        currentSession: {
          title: task.title,
          subtitle: `第${breakdown.indexOf(currentStage) + 1}阶段：${currentStage.title}`
        }
      })
    }

    // 显示拆解建议弹窗
    this.setData({ showBreakdownModal: true })
  },

  // 评估任务复杂度
  assessTaskComplexity(task) {
    let complexity = 'medium' // simple, medium, complex

    // 根据任务标题长度判断
    if (task.title.length > 20) complexity = 'complex'
    else if (task.title.length < 10) complexity = 'simple'

    // 根据任务描述判断
    if (task.description && task.description.length > 100) complexity = 'complex'

    // 根据子任务数量判断
    if (task.subtasks && task.subtasks.length > 3) complexity = 'complex'
    else if (task.subtasks && task.subtasks.length === 0) complexity = 'simple'

    return complexity
  },

  // 创建拆解计划
  createBreakdownPlan(task, complexity) {
    const plans = {
      simple: [
        { title: '理解和准备', description: '阅读任务要求，准备学习材料', duration: 25, completed: false },
        { title: '执行完成', description: '专注完成主要内容', duration: 25, completed: false }
      ],
      medium: [
        { title: '概念理解', description: '理解基本概念和原理', duration: 25, completed: false },
        { title: '实践练习', description: '通过练习巩固理解', duration: 25, completed: false },
        { title: '总结归纳', description: '整理知识点，形成体系', duration: 25, completed: false }
      ],
      complex: [
        { title: '概念理解', description: '深入理解核心概念', duration: 25, completed: false },
        { title: '例题分析', description: '分析典型例题和方法', duration: 25, completed: false },
        { title: '练习巩固', description: '大量练习提升熟练度', duration: 25, completed: false },
        { title: '难点突破', description: '攻克重点难点问题', duration: 25, completed: false },
        { title: '总结提升', description: '总结规律，提升能力', duration: 25, completed: false }
      ]
    }

    return plans[complexity] || plans.medium
  },

  // 隐藏拆解建议弹窗
  hideBreakdownModal() {
    this.setData({ showBreakdownModal: false })
  },

  // 确认拆解计划
  confirmBreakdown() {
    this.hideBreakdownModal()
    wx.showToast({
      title: '计划已确认',
      icon: 'success'
    })
  },

  // 自定义拆解
  customizeBreakdown() {
    wx.showToast({
      title: '自定义功能开发中',
      icon: 'none'
    })
  },

  // 声音系统相关方法
  selectBackgroundSound(e) {
    const soundId = e.currentTarget.dataset.sound
    this.setData({ selectedBgSound: soundId })

    // 停止当前背景音
    this.stopBackgroundAudio()

    // 播放新的背景音
    if (soundId !== 'silent') {
      this.playBackgroundSound(soundId)
    }

    this.saveUserSettings()
  },

  // 播放背景音
  playBackgroundSound(soundId) {
    if (!this.backgroundAudio || soundId === 'silent') return

    try {
      // 模拟音频文件路径（实际应该是真实的音频文件）
      const soundFiles = {
        rain: '/sounds/rain.mp3',
        ocean: '/sounds/ocean.mp3',
        cafe: '/sounds/cafe.mp3',
        forest: '/sounds/forest.mp3',
        whitenoise: '/sounds/whitenoise.mp3'
      }

      if (soundFiles[soundId]) {
        this.backgroundAudio.src = soundFiles[soundId]
        this.backgroundAudio.play()
      }
    } catch (error) {
      console.error('播放背景音失败:', error)
    }
  },

  // 停止背景音
  stopBackgroundAudio() {
    if (this.backgroundAudio) {
      this.backgroundAudio.stop()
    }
  },

  // 暂停背景音
  pauseBackgroundAudio() {
    if (this.backgroundAudio) {
      this.backgroundAudio.pause()
    }
  },

  // 调整音量
  adjustVolume(e) {
    const volume = e.detail.value
    this.setData({ volume })

    if (this.backgroundAudio) {
      this.backgroundAudio.volume = volume / 100
    }

    this.saveUserSettings()
  },

  // 调整背景音量
  adjustBgVolume(e) {
    const volume = e.detail.value
    this.setData({ bgVolume: volume })

    if (this.backgroundAudio) {
      this.backgroundAudio.volume = volume / 100
    }

    this.saveUserSettings()
  },

  // 调整提示音量
  adjustNotificationVolume(e) {
    const volume = e.detail.value
    this.setData({ notificationVolume: volume })
    this.saveUserSettings()
  },

  // 切换提示音
  toggleNotificationSound(e) {
    const soundId = e.currentTarget.dataset.sound
    const enabled = e.detail.value

    const sounds = this.data.notificationSounds.map(sound => {
      if (sound.id === soundId) {
        return { ...sound, enabled }
      }
      return sound
    })

    this.setData({ notificationSounds: sounds })
    this.saveUserSettings()
  },

  // 预览声音
  previewSound(e) {
    const soundId = e.currentTarget.dataset.sound

    // 临时播放预览
    if (soundId !== 'silent') {
      this.playBackgroundSound(soundId)

      // 3秒后停止预览
      setTimeout(() => {
        this.stopBackgroundAudio()
        // 恢复原来的背景音
        if (this.data.selectedBgSound !== 'silent') {
          this.playBackgroundSound(this.data.selectedBgSound)
        }
      }, 3000)
    }
  },

  // 显示声音设置
  showSoundSettings() {
    this.setData({ showSoundModal: true })
  },

  // 隐藏声音设置
  hideSoundModal() {
    this.setData({ showSoundModal: false })
  },

  // 切换声音设置显示
  toggleSoundSettings() {
    this.setData({ showSoundModal: !this.data.showSoundModal })
  },

  // 专注模式相关方法
  enterFocusMode() {
    this.setData({ focusMode: true })

    // 保持屏幕常亮
    wx.setKeepScreenOn({
      keepScreenOn: true
    })

    // 播放开始提示音
    this.playNotificationSound('start')
  },

  // 退出专注模式
  exitFocusMode() {
    this.setData({ focusMode: false })

    // 取消屏幕常亮
    wx.setKeepScreenOn({
      keepScreenOn: false
    })
  },

  // 播放提示音
  playNotificationSound(type) {
    const sound = this.data.notificationSounds.find(s => s.id === type)
    if (!sound || !sound.enabled) return

    try {
      // 创建临时音频上下文播放提示音
      const audio = wx.createInnerAudioContext()
      audio.src = `/sounds/${type}.mp3`
      audio.volume = this.data.notificationVolume / 100
      audio.play()

      audio.onEnded(() => {
        audio.destroy()
      })

      audio.onError(() => {
        audio.destroy()
      })

    } catch (error) {
      console.error('播放提示音失败:', error)
    }
  },

  // 计时器核心功能
  toggleTimer() {
    if (this.data.isRunning) {
      this.pauseTimer()
    } else {
      this.startTimer()
    }
  },

  // 开始计时器
  startTimer() {
    this.setData({
      isRunning: true,
      isPaused: false
    })

    // 播放开始提示音
    this.playNotificationSound('start')

    // 开始背景音
    if (this.data.selectedBgSound !== 'silent') {
      this.playBackgroundSound(this.data.selectedBgSound)
    }

    // 启动计时器
    this.timer = setInterval(() => {
      this.updateTimer()
    }, 1000)

    // 保持屏幕常亮
    wx.setKeepScreenOn({
      keepScreenOn: true
    })
  },

  // 暂停计时器
  pauseTimer() {
    this.setData({
      isRunning: false,
      isPaused: true
    })

    // 播放暂停提示音
    this.playNotificationSound('pause')

    // 暂停背景音
    this.pauseBackgroundAudio()

    // 清除计时器
    if (this.timer) {
      clearInterval(this.timer)
      this.timer = null
    }

    // 取消屏幕常亮
    wx.setKeepScreenOn({
      keepScreenOn: false
    })
  },

  // 停止计时器
  stopTimer() {
    wx.showModal({
      title: '确认停止',
      content: '确定要停止当前的专注会话吗？',
      success: (res) => {
        if (res.confirm) {
          this.resetTimer()
        }
      }
    })
  },

  // 重置计时器
  resetTimer() {
    this.setData({
      isRunning: false,
      isPaused: false
    })

    // 停止背景音
    this.stopBackgroundAudio()

    // 清除计时器
    if (this.timer) {
      clearInterval(this.timer)
      this.timer = null
    }

    // 重置时间
    const duration = this.data.timerState === 'work' ? this.data.workDuration :
                    this.data.timerState === 'shortBreak' ? this.data.shortBreakDuration :
                    this.data.longBreakDuration

    const totalTime = duration * 60
    this.setData({
      currentTime: totalTime,
      totalTime: totalTime,
      displayTime: this.formatTime(totalTime),
      progressDegree: 0
    })

    // 取消屏幕常亮
    wx.setKeepScreenOn({
      keepScreenOn: false
    })

    // 退出专注模式
    if (this.data.focusMode) {
      this.exitFocusMode()
    }
  },

  // 更新计时器
  updateTimer() {
    let currentTime = this.data.currentTime - 1

    if (currentTime <= 0) {
      // 时间到了
      this.completeSession()
      return
    }

    // 时间警告（最后1分钟）
    if (currentTime === 60) {
      this.playNotificationSound('warning')
    }

    // 更新显示
    const progressDegree = ((this.data.totalTime - currentTime) / this.data.totalTime) * 360

    this.setData({
      currentTime,
      displayTime: this.formatTime(currentTime),
      progressDegree
    })
  },

  // 完成会话
  completeSession() {
    // 停止计时器
    if (this.timer) {
      clearInterval(this.timer)
      this.timer = null
    }

    this.setData({
      isRunning: false,
      isPaused: false
    })

    // 播放完成提示音
    this.playNotificationSound('complete')

    // 停止背景音
    this.stopBackgroundAudio()

    // 震动提醒
    wx.vibrateShort()

    // 保存会话记录
    this.saveSessionRecord()

    // 更新任务进度
    this.updateTaskProgress()

    // 跳转到完成页面
    wx.navigateTo({
      url: '/pages/pomodoro-complete/index?type=pomodoro'
    })
  },

  // 保存会话记录
  async saveSessionRecord() {
    try {
      const duration = this.data.timerState === 'work' ? this.data.workDuration :
                      this.data.timerState === 'shortBreak' ? this.data.shortBreakDuration :
                      this.data.longBreakDuration

      const pomodoroData = {
        subject: this.data.selectedTask ? this.data.selectedTask.title : '自由专注',
        taskId: this.data.selectedTask ? this.data.selectedTask.id : null,
        duration: duration,
        completed: true,
        startTime: new Date(Date.now() - duration * 60 * 1000), // 计算开始时间
        endTime: new Date(),
        sessionType: this.data.timerState, // work, shortBreak, longBreak
        backgroundSound: this.data.selectedBgSound,
        studyMode: this.data.studyMode,
        focusMode: this.data.focusMode
      }

      // 只有工作会话才保存到云数据库
      if (this.data.timerState === 'work') {
        const result = await SmartApi.addPomodoroSession(pomodoroData)
        if (result.success) {
          console.log('番茄钟会话保存成功:', result.data)
        } else {
          console.error('保存番茄钟会话失败:', result.error)
        }
      }

      // 记录保存完成
      console.log('番茄钟会话记录保存完成')

    } catch (error) {
      console.error('保存会话记录失败:', error)
    }
  },

  // 更新任务进度
  async updateTaskProgress() {
    if (!this.data.selectedTask || this.data.timerState !== 'work') return

    try {
      // 使用SmartApi更新任务进度
      const result = await SmartApi.updateTask(this.data.selectedTask.id, {
        completedPomodoros: (this.data.selectedTask.completedPomodoros || 0) + 1,
        lastPomodoroAt: new Date().toISOString()
      })

      if (result.success) {
        // 更新当前任务显示
        this.setData({
          selectedTask: {
            ...this.data.selectedTask,
            completedPomodoros: (this.data.selectedTask.completedPomodoros || 0) + 1
          }
        })
        console.log('任务进度更新成功')
      } else {
        console.error('更新任务进度失败:', result.error)
      }

    } catch (error) {
      console.error('更新任务进度失败:', error)
    }
  },

  // 辅助方法
  formatTime(seconds) {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
  },

  // 更新计时器显示
  updateTimerDisplay() {
    const duration = this.data.timerState === 'work' ? this.data.workDuration :
                    this.data.timerState === 'shortBreak' ? this.data.shortBreakDuration :
                    this.data.longBreakDuration

    const totalTime = duration * 60
    this.setData({
      currentTime: totalTime,
      totalTime: totalTime,
      displayTime: this.formatTime(totalTime),
      progressDegree: 0,
      timerColor: this.data.timerState === 'work' ? '#FF6B6B' : '#4ECDC4'
    })
  },

  // 更新会话信息
  updateSessionInfo() {
    const stateTexts = {
      work: '专注时间',
      shortBreak: '短休息',
      longBreak: '长休息'
    }

    const sessionTypes = {
      work: '专注中',
      shortBreak: '休息中',
      longBreak: '长休息中'
    }

    this.setData({
      timerStateText: stateTexts[this.data.timerState],
      sessionTypeText: sessionTypes[this.data.timerState]
    })
  },

  // 更新今日统计
  updateTodayStats() {
    try {
      const today = new Date().toISOString().split('T')[0]
      const records = wx.getStorageSync('pomodoroRecords') || []

      // 筛选今日记录
      const todayRecords = records.filter(record => {
        return record.completedAt && record.completedAt.startsWith(today)
      })

      // 计算统计
      const workSessions = todayRecords.filter(r => r.type === 'work').length
      const totalMinutes = todayRecords.reduce((sum, record) => sum + (record.duration || 0), 0)
      const hours = Math.floor(totalMinutes / 60)
      const minutes = totalMinutes % 60

      this.setData({
        completedSessions: workSessions,
        todayFocus: hours > 0 ? `${hours}h${minutes}m` : `${minutes}m`
      })

    } catch (error) {
      console.error('更新今日统计失败:', error)
    }
  },

  // 快捷操作
  viewStatistics() {
    wx.navigateTo({
      url: '/pages/data-center/index'
    })
  },

  manageTask() {
    wx.switchTab({
      url: '/pages/task-center/index'
    })
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止事件冒泡
  },

  // 清理资源
  cleanup() {
    // 清除计时器
    if (this.timer) {
      clearInterval(this.timer)
      this.timer = null
    }

    // 停止音频
    this.stopBackgroundAudio()

    if (this.backgroundAudio) {
      this.backgroundAudio.destroy()
      this.backgroundAudio = null
    }

    // 取消屏幕常亮
    wx.setKeepScreenOn({
      keepScreenOn: false
    })
  },

  // 页面显示时更新TabBar状态
  updateTabBarState() {
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 3
      })
    }
  },

  // 兼容旧版本的方法（保留以防其他地方调用）
  loadData() {
    this.refreshTaskList()
    this.updateTodayStats()
  },

  // 新增的简洁交互方法
  showSettings() {
    this.setData({ showSettings: true })
  },

  hideSettings() {
    this.setData({ showSettings: false })
  },

  // 调整时间设置
  adjustTime(e) {
    const { type, action } = e.currentTarget.dataset
    const { workDuration, shortBreakDuration, longBreakDuration } = this.data

    let newValue
    if (type === 'work') {
      newValue = action === 'increase' ? workDuration + 5 : workDuration - 5
      newValue = Math.max(5, Math.min(60, newValue))
      this.setData({ workDuration: newValue })
    } else if (type === 'shortBreak') {
      newValue = action === 'increase' ? shortBreakDuration + 1 : shortBreakDuration - 1
      newValue = Math.max(1, Math.min(30, newValue))
      this.setData({ shortBreakDuration: newValue })
    } else if (type === 'longBreak') {
      newValue = action === 'increase' ? longBreakDuration + 5 : longBreakDuration - 5
      newValue = Math.max(5, Math.min(60, newValue))
      this.setData({ longBreakDuration: newValue })
    }

    this.saveUserSettings()

    // 如果当前没有运行，更新计时器
    if (!this.data.isRunning && !this.data.isPaused) {
      this.updateTimerDisplay()
    }
  }
})