/* pages/share-plan/index.wxss */
.container {
  padding: 20rpx;
  background: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 页面标题 */
.page-header {
  text-align: center;
  padding: 40rpx 20rpx;
  background: white;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
}

.page-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 12rpx;
}

.page-subtitle {
  font-size: 28rpx;
  color: #666;
}

/* 区块样式 */
.section {
  background: white;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

/* 小组卡片 */
.group-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.group-info {
  flex: 1;
}

.group-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.exam-name {
  font-size: 26rpx;
  color: #666;
}

.member-count {
  background: #e3f2fd;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}

.member-count text {
  font-size: 24rpx;
  color: #1890ff;
}

/* 考试卡片 */
.exam-card {
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.exam-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  display: block;
  margin-bottom: 12rpx;
}

.exam-subject {
  font-size: 26rpx;
  color: #666;
  display: block;
  margin-bottom: 8rpx;
}

.exam-date {
  font-size: 24rpx;
  color: #999;
}

/* 计划摘要 */
.plan-summary {
  display: flex;
  gap: 32rpx;
  margin-bottom: 24rpx;
}

.summary-item {
  flex: 1;
  text-align: center;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.summary-label {
  font-size: 24rpx;
  color: #666;
  display: block;
  margin-bottom: 8rpx;
}

.summary-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #1890ff;
}

/* 任务列表 */
.task-list {
  max-height: 600rpx;
  overflow-y: auto;
}

.task-item {
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
}

.task-item:last-child {
  margin-bottom: 0;
}

.task-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12rpx;
}

.task-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  flex: 1;
}

.task-priority {
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  color: white;
}

.priority-high {
  background: #ff4d4f;
}

.priority-medium {
  background: #fa8c16;
}

.priority-low {
  background: #52c41a;
}

.task-subject {
  font-size: 24rpx;
  color: #1890ff;
  display: block;
  margin-bottom: 8rpx;
}

.task-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  display: block;
  margin-bottom: 12rpx;
}

.task-meta {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.task-duration {
  font-size: 22rpx;
  color: #999;
}

.task-status {
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  font-size: 20rpx;
}

.status-completed {
  background: #f6ffed;
  color: #52c41a;
}

.status-pending {
  background: #fff7e6;
  color: #fa8c16;
}

/* 空状态 */
.empty-tasks {
  text-align: center;
  padding: 60rpx 20rpx;
}

.empty-icon {
  font-size: 80rpx;
  display: block;
  margin-bottom: 16rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  display: block;
  margin-bottom: 12rpx;
}

.empty-tip {
  font-size: 24rpx;
  color: #ccc;
}

/* 分享提示 */
.share-tips {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.tip-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.tip-icon {
  font-size: 32rpx;
  width: 48rpx;
  text-align: center;
}

.tip-text {
  font-size: 26rpx;
  color: #666;
}

/* 底部按钮 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 24rpx;
  background: white;
  border-top: 1rpx solid #eee;
}

.btn-large {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  font-size: 32rpx;
}

/* 按钮样式 */
.btn {
  border: none;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  text-align: center;
  transition: all 0.3s ease;
}

.btn-primary {
  background: #1890ff;
  color: white;
}

.btn-primary:disabled {
  background: #ccc;
  color: #999;
}
