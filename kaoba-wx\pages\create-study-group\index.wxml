<!-- pages/create-study-group/index.wxml -->
<view class="container">
  <!-- 页面标题 -->
  <view class="page-header">
    <text class="page-title">创建搭子小组</text>
    <text class="page-subtitle">选择考试，邀请朋友一起备考</text>
  </view>

  <!-- 选择考试 -->
  <view class="section">
    <view class="section-title">选择考试</view>
    <view class="exam-list">
      <view 
        class="exam-item {{selectedExam && selectedExam.id === item.id ? 'selected' : ''}}"
        wx:for="{{examList}}" 
        wx:key="id"
        bindtap="onSelectExam"
        data-exam="{{item}}"
      >
        <view class="exam-info">
          <text class="exam-name">{{item.name}}</text>
          <text class="exam-subject">{{item.subject}}</text>
        </view>
        <view class="exam-date" wx:if="{{item.date}}">
          <text>{{item.date}}</text>
        </view>
        <view class="check-icon" wx:if="{{selectedExam && selectedExam.id === item.id}}">
          <text>✓</text>
        </view>
      </view>
    </view>
    
    <view class="empty-exams" wx:if="{{examList.length === 0}}">
      <text class="empty-icon">📚</text>
      <text class="empty-text">还没有考试计划</text>
      <button class="btn btn-secondary" bindtap="addExam">添加考试</button>
    </view>
  </view>

  <!-- 小组名称 -->
  <view class="section">
    <view class="section-title">小组名称（可选）</view>
    <view class="input-group">
      <input 
        class="form-input"
        placeholder="{{selectedExam ? selectedExam.name + '搭子小组' : '请先选择考试'}}"
        value="{{groupName}}"
        bindinput="onGroupNameInput"
        disabled="{{!selectedExam}}"
      />
    </view>
    <view class="input-tip">
      <text>不填写将使用默认名称</text>
    </view>
  </view>

  <!-- 小组说明 -->
  <view class="section">
    <view class="group-info">
      <view class="info-item">
        <text class="info-icon">👥</text>
        <text class="info-text">最多3人小组</text>
      </view>
      <view class="info-item">
        <text class="info-icon">📋</text>
        <text class="info-text">可以分享复习计划</text>
      </view>
      <view class="info-item">
        <text class="info-icon">📊</text>
        <text class="info-text">查看彼此进度</text>
      </view>
      <view class="info-item">
        <text class="info-icon">👍</text>
        <text class="info-text">互相点赞鼓励</text>
      </view>
    </view>
  </view>

  <!-- 创建按钮 -->
  <view class="bottom-actions">
    <button 
      class="btn btn-primary btn-large"
      bindtap="onCreateGroup"
      disabled="{{!selectedExam || loading}}"
      loading="{{loading}}"
    >
      {{loading ? '创建中...' : '创建搭子小组'}}
    </button>
  </view>
</view>
