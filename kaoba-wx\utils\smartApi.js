// 云开发API - 统一使用微信云开发
const CloudApi = require('./cloudApi')

class SmartApi {


  // 任务管理API - 强制使用云开发
  static async getTasks(filter = {}, limit = 20, skip = 0) {
    console.log('使用云开发API获取任务')
    return await CloudApi.getTasks(filter, limit, skip)
  }

  static async addTask(taskData) {
    console.log('使用云开发API添加任务')
    return await CloudApi.addTask(taskData)
  }

  static async updateTask(taskId, updates) {
    console.log('使用云开发API更新任务')
    return await CloudApi.updateTask(taskId, updates)
  }

  static async deleteTask(taskId) {
    console.log('使用云开发API删除任务')
    return await CloudApi.deleteTask(taskId)
  }

  static async completeTask(taskId, completed = true) {
    console.log('使用云开发API完成任务')
    return await CloudApi.completeTask(taskId, completed)
  }

  static async getTaskStats(dateRange = null) {
    console.log('使用云开发API获取任务统计')
    return await CloudApi.getTaskStats(dateRange)
  }

  // 考试管理API
  static async getExams(filter = {}, limit = 20, skip = 0) {
    console.log('使用云开发API获取考试')
    return await CloudApi.getExams(filter, limit, skip)
  }

  static async addExam(examData) {
    console.log('使用云开发API添加考试')
    return await CloudApi.addExam(examData)
  }

  static async updateExam(examId, updates) {
    console.log('使用云开发API更新考试')
    return await CloudApi.updateExam(examId, updates)
  }

  static async deleteExam(examId) {
    console.log('使用云开发API删除考试')
    return await CloudApi.deleteExam(examId)
  }

  static async getUpcomingExams(days = 7, limit = 10) {
    console.log('使用云开发API获取即将到来的考试')
    return await CloudApi.getUpcomingExams(days, limit)
  }

  static async getExamStats(dateRange = null) {
    console.log('使用云开发API获取考试统计')
    return await CloudApi.getExamStats(dateRange)
  }

  // 学习管理API - 强制使用云开发
  static async getStudyStats(dateRange = null, groupBy = 'day') {
    console.log('使用云开发API获取学习统计')
    return await CloudApi.getStudyStats(dateRange, groupBy)
  }

  static async getPomodoroStats(dateRange = null) {
    console.log('使用云开发API获取番茄钟统计')
    return await CloudApi.getPomodoroStats(dateRange)
  }

  static async addPomodoroSession(pomodoroData) {
    console.log('使用云开发API添加番茄钟会话')
    return await CloudApi.addPomodoroSession(pomodoroData)
  }

  // 初始化数据 - 强制使用云开发
  static async initData() {
    console.log('使用云开发初始化数据')
    try {
      const result = await wx.cloud.callFunction({
        name: 'initDatabase',
        data: { action: 'init' }
      })
      return result.result || { success: false, error: '云函数调用失败' }
    } catch (error) {
      console.error('云开发初始化失败:', error)
      return { success: false, error: error.message }
    }
  }


}

module.exports = SmartApi
