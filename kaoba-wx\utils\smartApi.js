// 云开发API - 统一使用微信云开发
const CloudApi = require('./cloudApi')

class SmartApi {


  // 任务管理API - 强制使用云开发
  static async getTasks(filter = {}, limit = 20, skip = 0) {
    console.log('使用云开发API获取任务')
    return await CloudApi.getTasks(filter, limit, skip)
  }

  static async addTask(taskData) {
    console.log('使用云开发API添加任务')
    return await CloudApi.addTask(taskData)
  }

  static async updateTask(taskId, updates) {
    console.log('使用云开发API更新任务')
    return await CloudApi.updateTask(taskId, updates)
  }

  static async deleteTask(taskId) {
    console.log('使用云开发API删除任务')
    return await CloudApi.deleteTask(taskId)
  }

  static async completeTask(taskId, completed = true) {
    console.log('使用云开发API完成任务')
    return await CloudApi.completeTask(taskId, completed)
  }

  static async getTaskStats(dateRange = null) {
    console.log('使用云开发API获取任务统计')
    return await CloudApi.getTaskStats(dateRange)
  }

  // 考试管理API
  static async getExams(filter = {}, limit = 20, skip = 0) {
    console.log('使用云开发API获取考试')
    return await CloudApi.getExams(filter, limit, skip)
  }

  static async addExam(examData) {
    console.log('使用云开发API添加考试')
    return await CloudApi.addExam(examData)
  }

  static async updateExam(examId, updates) {
    console.log('使用云开发API更新考试')
    return await CloudApi.updateExam(examId, updates)
  }

  static async deleteExam(examId) {
    console.log('使用云开发API删除考试')
    return await CloudApi.deleteExam(examId)
  }

  static async getUpcomingExams(days = 7, limit = 10) {
    console.log('使用云开发API获取即将到来的考试')
    return await CloudApi.getUpcomingExams(days, limit)
  }

  static async getExamStats(dateRange = null) {
    console.log('使用云开发API获取考试统计')
    return await CloudApi.getExamStats(dateRange)
  }

  // 学习管理API - 强制使用云开发
  static async getStudyStats(dateRange = null, groupBy = 'day') {
    console.log('使用云开发API获取学习统计')
    return await CloudApi.getStudyStats(dateRange, groupBy)
  }

  static async getPomodoroStats(dateRange = null) {
    console.log('使用云开发API获取番茄钟统计')
    return await CloudApi.getPomodoroStats(dateRange)
  }

  static async addPomodoroSession(pomodoroData) {
    console.log('使用云开发API添加番茄钟会话')
    return await CloudApi.addPomodoroSession(pomodoroData)
  }

  // 初始化数据 - 强制使用云开发
  static async initData() {
    console.log('使用云开发初始化数据')
    try {
      const result = await wx.cloud.callFunction({
        name: 'initDatabase',
        data: { action: 'init' }
      })
      return result.result || { success: false, error: '云函数调用失败' }
    } catch (error) {
      console.error('云开发初始化失败:', error)
      return { success: false, error: error.message }
    }
  }

  // 用户管理API
  static async cleanDuplicateUsers() {
    console.log('清理重复用户数据')
    try {
      const result = await wx.cloud.callFunction({
        name: 'userManager',
        data: { action: 'cleanDuplicateUsers' }
      })
      return result.result
    } catch (error) {
      console.error('清理重复用户失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async getUserData() {
    console.log('获取用户数据统计')
    try {
      const result = await wx.cloud.callFunction({
        name: 'userManager',
        data: { action: 'getUserData' }
      })
      return result.result
    } catch (error) {
      console.error('获取用户数据失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async deleteUserData() {
    console.log('删除用户所有数据')
    try {
      const result = await wx.cloud.callFunction({
        name: 'userManager',
        data: { action: 'deleteUserData' }
      })
      return result.result
    } catch (error) {
      console.error('删除用户数据失败:', error)
      return { success: false, error: error.message }
    }
  }

  // 头像上传API
  static async uploadAvatar(filePath) {
    console.log('SmartApi: 开始上传用户头像, filePath:', filePath)

    try {
      // 检查参数
      if (!filePath) {
        throw new Error('文件路径不能为空')
      }

      // 先获取上传路径
      console.log('SmartApi: 获取上传路径')
      const pathResult = await wx.cloud.callFunction({
        name: 'uploadAvatar',
        data: {
          action: 'getUploadUrl',
          data: {
            fileName: filePath.split('/').pop() || 'avatar.jpg',
            fileType: 'image'
          }
        }
      })

      console.log('SmartApi: 获取上传路径结果:', pathResult)

      if (!pathResult.result || !pathResult.result.success) {
        return pathResult.result || { success: false, error: '获取上传路径失败' }
      }

      const cloudPath = pathResult.result.data.cloudPath
      console.log('SmartApi: 云存储路径:', cloudPath)

      // 上传文件到云存储
      console.log('SmartApi: 开始上传文件到云存储')
      const uploadResult = await wx.cloud.uploadFile({
        cloudPath: cloudPath,
        filePath: filePath
      })

      console.log('SmartApi: 云存储上传结果:', uploadResult)

      if (uploadResult.fileID) {
        // 调用云函数更新用户头像
        console.log('SmartApi: 更新用户头像记录')
        const updateResult = await wx.cloud.callFunction({
          name: 'uploadAvatar',
          data: {
            action: 'uploadAvatar',
            data: {
              fileID: uploadResult.fileID,
              fileName: filePath.split('/').pop() || 'avatar.jpg',
              fileSize: uploadResult.fileSize || 0
            }
          }
        })

        console.log('SmartApi: 更新用户头像结果:', updateResult)
        return updateResult.result || { success: false, error: '更新用户头像记录失败' }
      } else {
        return { success: false, error: '文件上传到云存储失败' }
      }
    } catch (error) {
      console.error('SmartApi: 上传头像异常:', error)
      return { success: false, error: error.message || '上传头像失败' }
    }
  }

  static async deleteAvatar(fileID) {
    console.log('删除用户头像')
    try {
      const result = await wx.cloud.callFunction({
        name: 'uploadAvatar',
        data: {
          action: 'deleteAvatar',
          data: { fileID }
        }
      })
      return result.result
    } catch (error) {
      console.error('删除头像失败:', error)
      return { success: false, error: error.message }
    }
  }
}

module.exports = SmartApi
