// 云开发API - 统一使用微信云开发
const CloudApi = require('./cloudApi')

class SmartApi {


  // 复习计划管理API - 强制使用云开发
  static async getTasks(filter = {}, limit = 20, skip = 0) {
    console.log('使用云开发API获取复习计划')
    return await CloudApi.getTasks(filter, limit, skip)
  }

  static async getTaskById(taskId) {
    console.log('使用云开发API获取复习计划详情')
    return await CloudApi.getTaskById(taskId)
  }

  static async addTask(taskData) {
    console.log('使用云开发API添加复习计划')
    return await CloudApi.addTask(taskData)
  }

  static async updateTask(taskId, updates) {
    console.log('使用云开发API更新复习计划')
    return await CloudApi.updateTask(taskId, updates)
  }

  static async deleteTask(taskId) {
    console.log('使用云开发API删除复习计划')
    return await CloudApi.deleteTask(taskId)
  }

  static async completeTask(taskId, completed = true) {
    console.log('使用云开发API完成复习')
    return await CloudApi.completeTask(taskId, completed)
  }

  static async getTaskStats(dateRange = null) {
    console.log('使用云开发API获取复习统计')
    return await CloudApi.getTaskStats(dateRange)
  }

  // 考试管理API
  static async getExams(filter = {}, limit = 20, skip = 0) {
    console.log('使用云开发API获取考试')
    return await CloudApi.getExams(filter, limit, skip)
  }

  static async getExamById(examId) {
    console.log('使用云开发API获取考试详情')
    return await CloudApi.getExamById(examId)
  }

  static async addExam(examData) {
    console.log('使用云开发API添加考试')
    return await CloudApi.addExam(examData)
  }

  static async updateExam(examId, updates) {
    console.log('使用云开发API更新考试')
    return await CloudApi.updateExam(examId, updates)
  }

  static async deleteExam(examId) {
    console.log('使用云开发API删除考试')
    return await CloudApi.deleteExam(examId)
  }

  static async getUpcomingExams(days = 7, limit = 10) {
    console.log('使用云开发API获取即将到来的考试')
    return await CloudApi.getUpcomingExams(days, limit)
  }

  static async getExamStats(dateRange = null) {
    console.log('使用云开发API获取考试统计')
    return await CloudApi.getExamStats(dateRange)
  }

  // 复习管理API - 强制使用云开发
  static async getStudyStats(dateRange = null, groupBy = 'day') {
    console.log('使用云开发API获取复习统计')
    return await CloudApi.getStudyStats(dateRange, groupBy)
  }

  // 统计数据管理API
  static async getUserStats(timeRange = 'week') {
    console.log('使用云开发API获取用户统计')
    return await CloudApi.getUserStats(timeRange)
  }

  static async updateDailyStats(statsData) {
    console.log('使用云开发API更新每日统计')
    return await CloudApi.updateDailyStats(statsData)
  }

  static async getDailyStats(date = null) {
    console.log('使用云开发API获取每日统计')
    return await CloudApi.getDailyStats(date)
  }

  // 成就系统API
  static async getUserAchievements(category = null) {
    console.log('使用云开发API获取用户成就')
    return await CloudApi.getUserAchievements(category)
  }

  static async checkAchievements(triggerType, value) {
    console.log('使用云开发API检查成就')
    return await CloudApi.checkAchievements(triggerType, value)
  }

  static async getAchievementStats() {
    console.log('使用云开发API获取成就统计')
    return await CloudApi.getAchievementStats()
  }

  static async getPomodoroStats(dateRange = null) {
    console.log('使用云开发API获取番茄钟统计')
    return await CloudApi.getPomodoroStats(dateRange)
  }

  static async addPomodoroSession(pomodoroData) {
    console.log('使用云开发API添加番茄钟会话')
    return await CloudApi.addPomodoroSession(pomodoroData)
  }

  // 初始化数据 - 强制使用云开发
  static async initData() {
    console.log('使用云开发初始化数据')
    try {
      const result = await wx.cloud.callFunction({
        name: 'initDatabase',
        data: { action: 'init' }
      })
      return result.result || { success: false, error: '云函数调用失败' }
    } catch (error) {
      console.error('云开发初始化失败:', error)
      return { success: false, error: error.message }
    }
  }

  // 用户管理API
  static async cleanDuplicateUsers() {
    console.log('清理重复用户数据')
    try {
      const result = await wx.cloud.callFunction({
        name: 'userManager',
        data: { action: 'cleanDuplicateUsers' }
      })
      return result.result
    } catch (error) {
      console.error('清理重复用户失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async getUserData() {
    console.log('获取用户数据统计')
    try {
      const result = await wx.cloud.callFunction({
        name: 'userManager',
        data: { action: 'getUserData' }
      })
      return result.result
    } catch (error) {
      console.error('获取用户数据失败:', error)
      return { success: false, error: error.message }
    }
  }

  static async deleteUserData() {
    console.log('删除用户所有数据')
    try {
      const result = await wx.cloud.callFunction({
        name: 'userManager',
        data: { action: 'deleteUserData' }
      })
      return result.result
    } catch (error) {
      console.error('删除用户数据失败:', error)
      return { success: false, error: error.message }
    }
  }

  // 头像上传API
  static async uploadAvatar(filePath) {
    console.log('SmartApi: 开始上传用户头像, filePath:', filePath)

    try {
      // 检查参数
      if (!filePath) {
        throw new Error('文件路径不能为空')
      }

      // 先获取上传路径
      console.log('SmartApi: 获取上传路径')
      const pathResult = await new Promise((resolve, reject) => {
        wx.cloud.callFunction({
          name: 'uploadAvatar',
          data: {
            action: 'getUploadUrl',
            data: {
              fileName: filePath.split('/').pop() || 'avatar.jpg',
              fileType: 'image'
            }
          },
          success: resolve,
          fail: reject
        })
      })

      console.log('SmartApi: 获取上传路径结果:', JSON.stringify(pathResult, (key, value) => {
        // 安全地序列化，跳过Symbol值
        if (typeof value === 'symbol') {
          return value.toString()
        }
        return value
      }))

      if (!pathResult.result || !pathResult.result.success) {
        const errorResult = pathResult.result || { success: false, error: '获取上传路径失败' }
        console.log('SmartApi: 获取上传路径失败:', JSON.stringify(errorResult))
        return errorResult
      }

      const cloudPath = pathResult.result.data.cloudPath
      console.log('SmartApi: 云存储路径:', cloudPath)

      // 上传文件到云存储
      console.log('SmartApi: 开始上传文件到云存储')
      const uploadResult = await new Promise((resolve, reject) => {
        wx.cloud.uploadFile({
          cloudPath: cloudPath,
          filePath: filePath,
          success: (res) => {
            console.log('SmartApi: 云存储上传成功回调:', res)
            resolve(res)
          },
          fail: (error) => {
            console.error('SmartApi: 云存储上传失败回调:', error)
            reject(error)
          }
        })
      })

      console.log('SmartApi: 云存储上传结果:', JSON.stringify(uploadResult, (key, value) => {
        if (typeof value === 'symbol') {
          return value.toString()
        }
        return value
      }))

      if (uploadResult.fileID) {
        // 调用云函数更新用户头像
        console.log('SmartApi: 更新用户头像记录')

        // 准备云函数参数，确保没有Symbol值
        const functionData = {
          action: 'uploadAvatar',
          data: {
            fileID: String(uploadResult.fileID), // 确保是字符串
            fileName: String(filePath.split('/').pop() || 'avatar.jpg'),
            fileSize: Number(uploadResult.fileSize || 0)
          }
        }

        console.log('SmartApi: 云函数参数:', JSON.stringify(functionData))

        const updateResult = await new Promise((resolve, reject) => {
          wx.cloud.callFunction({
            name: 'uploadAvatar',
            data: functionData,
            success: (res) => {
              console.log('SmartApi: 云函数调用成功回调:', res)
              resolve(res)
            },
            fail: (error) => {
              console.error('SmartApi: 云函数调用失败回调:', error)
              reject(error)
            }
          })
        })

        console.log('SmartApi: 更新用户头像结果:', JSON.stringify(updateResult, (key, value) => {
          if (typeof value === 'symbol') {
            return value.toString()
          }
          return value
        }))

        if (updateResult.result) {
          // 安全地处理返回结果
          const result = updateResult.result
          console.log('SmartApi: 最终返回结果:', JSON.stringify(result))

          // 确保返回的数据不包含Symbol
          return {
            success: Boolean(result.success),
            data: result.data ? {
              avatarUrl: String(result.data.avatarUrl || ''),
              fileName: String(result.data.fileName || ''),
              fileSize: Number(result.data.fileSize || 0),
              uploadTime: String(result.data.uploadTime || '')
            } : null,
            error: result.error ? String(result.error) : null
          }
        } else {
          return { success: false, error: '更新用户头像记录失败' }
        }
      } else {
        return { success: false, error: '文件上传到云存储失败，未获得fileID' }
      }
    } catch (error) {
      console.error('SmartApi: 上传头像异常:', error)

      // 安全地获取错误信息
      let errorMessage = '上传头像失败'
      if (error && typeof error === 'object') {
        if (error.message && typeof error.message === 'string') {
          errorMessage = error.message
        } else if (error.errMsg && typeof error.errMsg === 'string') {
          errorMessage = error.errMsg
        }
      } else if (typeof error === 'string') {
        errorMessage = error
      }

      return { success: false, error: errorMessage }
    }
  }

  static async deleteAvatar(fileID) {
    console.log('删除用户头像')
    try {
      const result = await wx.cloud.callFunction({
        name: 'uploadAvatar',
        data: {
          action: 'deleteAvatar',
          data: { fileID }
        }
      })
      return result.result
    } catch (error) {
      console.error('删除头像失败:', error)
      return { success: false, error: error.message }
    }
  }
}

module.exports = SmartApi
