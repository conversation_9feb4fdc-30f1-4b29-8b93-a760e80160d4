// pages/exam-center/index.js
const SmartApi = require('../../utils/smartApi')

Page({
  data: {
    examStats: [],
    filterTabs: [],
    currentFilter: 'all',
    exams: [],
    filteredExams: [],
    showActionSheet: false,
    selectedExam: null,
    examActions: []
  },

  onLoad() {
    this.initPage()
  },

  onShow() {
    this.loadData()
    // 更新tabBar选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 1
      })
    }
  },

  onPullDownRefresh() {
    this.loadData()
    setTimeout(() => {
      wx.stopPullDownRefresh()
    }, 1000)
  },

  // 初始化页面
  initPage() {
    this.initFilterTabs()
    this.initExamActions()
    this.loadData()
  },

  // 初始化筛选标签
  initFilterTabs() {
    const filterTabs = [
      { value: 'all', label: '全部', count: 0 },
      { value: 'upcoming', label: '即将到来', count: 0 },
      { value: 'preparing', label: '备考中', count: 0 },
      { value: 'completed', label: '已完成', count: 0 }
    ]

    this.setData({ filterTabs })
  },

  // 初始化考试操作
  initExamActions() {
    const examActions = [
      { id: 'view', icon: '👁️', text: '查看详情', action: 'viewDetail' },
      { id: 'edit', icon: '✏️', text: '编辑考试', action: 'edit' },
      { id: 'study', icon: '📚', text: '开始复习', action: 'startStudy' },
      { id: 'tasks', icon: '📝', text: '查看任务', action: 'viewTasks' },
      { id: 'share', icon: '📤', text: '分享考试', action: 'share' },
      { id: 'delete', icon: '🗑️', text: '删除考试', action: 'delete' }
    ]

    this.setData({ examActions })
  },

  // 加载数据
  async loadData() {
    // 检查登录状态
    const app = getApp()
    if (!app.globalData.userInfo || !app.globalData.openid) {
      console.log('用户未登录，尝试自动登录...')

      const LoginApi = require('../../utils/loginApi')
      const loginResult = await LoginApi.login()

      if (!loginResult.success) {
        wx.showToast({
          title: '请先登录',
          icon: 'none'
        })
        setTimeout(() => {
          wx.navigateTo({
            url: '/pages/login/index'
          })
        }, 1500)
        return
      }
    }

    this.loadExamStats()
    this.loadExams()
  },

  // 加载考试统计
  async loadExamStats() {
    try {
      // 使用SmartApi获取考试统计
      const result = await SmartApi.getExamStats()

      if (result.success) {
        const stats = result.data
        const examStats = [
          { label: '总考试', value: stats.total.toString() },
          { label: '备考中', value: stats.preparing.toString() },
          { label: '已完成', value: stats.completed.toString() },
          { label: '即将到来', value: stats.upcoming.toString() }
        ]

        this.setData({ examStats })
      } else {
        // 如果获取统计失败，显示默认值
        const examStats = [
          { label: '总考试', value: '0' },
          { label: '备考中', value: '0' },
          { label: '已完成', value: '0' },
          { label: '即将到来', value: '0' }
        ]

        this.setData({ examStats })
      }
    } catch (error) {
      console.error('加载考试统计失败:', error)
      // 显示默认统计
      const examStats = [
        { label: '总考试', value: '0' },
        { label: '备考中', value: '0' },
        { label: '已完成', value: '0' },
        { label: '即将到来', value: '0' }
      ]

      this.setData({ examStats })
    }
  },

  // 加载考试列表
  async loadExams() {
    wx.showLoading({
      title: '加载中...',
      mask: true
    })

    try {
      // 使用SmartApi从云数据库获取考试
      const result = await SmartApi.getExams({}, 20, 0)

      wx.hideLoading()

      if (result.success) {
        // 转换数据格式以适配页面显示
        const exams = result.data.map(exam => this.formatExamData(exam))

        this.setData({ exams })
        this.filterExams()
        this.updateFilterCounts()
      } else {
        console.error('加载考试失败:', result.error)
        wx.showToast({
          title: '加载考试失败',
          icon: 'none'
        })
        this.setData({ exams: [] })
      }
    } catch (error) {
      wx.hideLoading()
      console.error('加载考试异常:', error)
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
      this.setData({ exams: [] })
    }
  },

  // 格式化考试数据
  formatExamData(exam) {
    const examDate = new Date(exam.examDate)
    const now = new Date()
    const timeDiff = examDate.getTime() - now.getTime()

    // 计算倒计时
    const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24))
    const hours = Math.floor((timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
    const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60))

    // 确定状态
    let status = 'preparing'
    let statusText = '备考中'
    let progressColor = '#1890FF'

    if (timeDiff < 0) {
      status = 'completed'
      statusText = '已结束'
      progressColor = '#999'
    } else if (days <= 7) {
      status = 'upcoming'
      statusText = '即将到来'
      progressColor = '#52C41A'
    } else if (days <= 30) {
      status = 'preparing'
      statusText = '备考中'
      progressColor = '#FA8C16'
    }

    return {
      id: exam._id,
      name: exam.title,
      type: exam.subject,
      date: exam.examDate,
      time: exam.examTime || '',
      location: exam.location || '',
      description: exam.description || '',
      status: status,
      statusText: statusText,
      preparationProgress: 0, // 可以后续添加进度计算逻辑
      progressColor: progressColor,
      countdown: timeDiff > 0 ? [
        { value: Math.max(0, days), unit: '天' },
        { value: Math.max(0, hours), unit: '时' },
        { value: Math.max(0, minutes), unit: '分' }
      ] : [
        { value: 0, unit: '天' },
        { value: 0, unit: '时' },
        { value: 0, unit: '分' }
      ],
      subjects: [] // 可以后续添加科目信息
    }
  },




  // 更新筛选计数
  updateFilterCounts() {
    const { exams, filterTabs } = this.data
    const updatedTabs = filterTabs.map(tab => {
      let count = 0
      if (tab.value === 'all') {
        count = exams.length
      } else {
        count = exams.filter(exam => exam.status === tab.value).length
      }
      return { ...tab, count }
    })

    this.setData({ filterTabs: updatedTabs })
  },

  // 筛选考试
  filterExams() {
    const { exams, currentFilter } = this.data
    let filteredExams = exams

    if (currentFilter !== 'all') {
      filteredExams = exams.filter(exam => exam.status === currentFilter)
    }

    // 按日期排序
    filteredExams.sort((a, b) => new Date(a.date) - new Date(b.date))

    this.setData({ filteredExams })
  },

  // 切换筛选
  switchFilter(e) {
    const filter = e.currentTarget.dataset.filter
    this.setData({ currentFilter: filter })
    this.filterExams()
  },

  // 获取空状态标题
  getEmptyTitle() {
    const { currentFilter } = this.data
    const titles = {
      all: '还没有考试',
      upcoming: '暂无即将到来的考试',
      preparing: '暂无备考中的考试',
      completed: '暂无已完成的考试'
    }
    return titles[currentFilter] || '暂无数据'
  },

  // 获取空状态消息
  getEmptyMessage() {
    const { currentFilter } = this.data
    const messages = {
      all: '添加你的第一个考试，开始高效备考',
      upcoming: '所有考试都在准备中',
      preparing: '没有正在备考的考试',
      completed: '还没有完成的考试'
    }
    return messages[currentFilter] || '暂无相关数据'
  },

  // 显示考试操作菜单
  showExamActions(e) {
    const exam = e.currentTarget.dataset.exam
    this.setData({
      selectedExam: exam,
      showActionSheet: true
    })
  },

  // 隐藏操作菜单
  hideActionSheet() {
    this.setData({
      showActionSheet: false,
      selectedExam: null
    })
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止事件冒泡
  },

  // 执行考试操作
  executeExamAction(e) {
    const action = e.currentTarget.dataset.action
    const exam = this.data.selectedExam

    this.hideActionSheet()

    switch (action) {
      case 'viewDetail':
        this.viewExamDetail({ currentTarget: { dataset: { id: exam.id } } })
        break
      case 'edit':
        this.editExam({ currentTarget: { dataset: { id: exam.id } } })
        break
      case 'startStudy':
        this.startStudyForExam({ currentTarget: { dataset: { id: exam.id } } })
        break
      case 'viewTasks':
        this.viewExamTasks({ currentTarget: { dataset: { id: exam.id } } })
        break
      case 'share':
        this.shareExam(exam)
        break
      case 'delete':
        this.deleteExam(exam)
        break
    }
  },

  // 页面跳转和操作方法
  openSearch() {
    wx.navigateTo({
      url: '/pages/search/index?type=exam'
    })
  },

  viewExamDetail(e) {
    const examId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: '/pages/exam-detail/index?id=' + examId
    })
  },

  addExam() {
    wx.navigateTo({
      url: '/pages/add-exam/index'
    })
  },

  editExam(e) {
    const examId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: '/pages/edit-exam/index?id=' + examId
    })
  },

  startStudyForExam(e) {
    const examId = e.currentTarget.dataset.id
    // 跳转到任务中心，筛选该考试的任务
    wx.switchTab({
      url: '/pages/task-center/index'
    })
  },

  viewExamTasks(e) {
    const examId = e.currentTarget.dataset.id
    wx.switchTab({
      url: '/pages/task-center/index'
    })
  },

  shareExam(exam) {
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    })
    
    wx.showToast({
      title: '分享功能开发中',
      icon: 'none'
    })
  },

  async deleteExam(exam) {
    const result = await wx.showModal({
      title: '确认删除',
      content: `确定要删除考试"${exam.name}"吗？此操作不可恢复。`,
      confirmText: '删除',
      confirmColor: '#FF4D4F'
    })

    if (!result.confirm) return

    wx.showLoading({
      title: '删除中...',
      mask: true
    })

    try {
      // 使用SmartApi删除考试
      const deleteResult = await SmartApi.deleteExam(exam.id)

      wx.hideLoading()

      if (deleteResult.success) {
        wx.showToast({
          title: '删除成功',
          icon: 'success'
        })

        // 重新加载数据
        this.loadData()
      } else {
        wx.showToast({
          title: deleteResult.error || '删除失败',
          icon: 'none'
        })
      }
    } catch (error) {
      wx.hideLoading()
      console.error('删除考试失败:', error)
      wx.showToast({
        title: '删除失败',
        icon: 'none'
      })
    }
  }
})
