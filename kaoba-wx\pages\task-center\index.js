// pages/task-center/index.js
const SmartApi = require('../../utils/smartApi')

Page({
  data: {
    taskStats: [],
    filterOptions: [],
    currentFilter: 'all',
    tasks: [],
    filteredTasks: [],
    groupedTasks: [],
    showActionSheet: false,
    selectedTask: null,
    taskActions: [],
    showFilterMenu: false,
    currentSort: 'dueDate',
    selectedPriorities: [],
    sortOptions: [],
    priorityOptions: []
  },

  onLoad() {
    this.initPage()
  },

  onShow() {
    this.loadData()
    // 更新tabBar选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 2
      })
    }
  },

  onPullDownRefresh() {
    this.loadData()
    setTimeout(() => {
      wx.stopPullDownRefresh()
    }, 1000)
  },

  // 初始化页面
  initPage() {
    this.initFilterOptions()
    this.initTaskActions()
    this.initSortOptions()
    this.loadData()
  },

  // 初始化筛选选项
  initFilterOptions() {
    const filterOptions = [
      { value: 'all', label: '全部', count: 0 },
      { value: 'today', label: '今天', count: 0 },
      { value: 'pending', label: '待完成', count: 0 },
      { value: 'completed', label: '已完成', count: 0 },
      { value: 'overdue', label: '已逾期', count: 0 }
    ]

    this.setData({ filterOptions })
  },

  // 初始化任务操作
  initTaskActions() {
    const taskActions = [
      { id: 'view', icon: '👁️', text: '查看详情', action: 'viewDetail' },
      { id: 'edit', icon: '✏️', text: '编辑计划', action: 'edit' },
      { id: 'start', icon: '▶️', text: '开始复习', action: 'start' },
      { id: 'complete', icon: '✅', text: '标记完成', action: 'complete' },
      { id: 'delete', icon: '🗑️', text: '删除计划', action: 'delete' }
    ]

    this.setData({ taskActions })
  },

  // 初始化排序选项
  initSortOptions() {
    const sortOptions = [
      { value: 'dueDate', label: '按截止时间' },
      { value: 'priority', label: '按优先级' },
      { value: 'createTime', label: '按创建时间' },
      { value: 'progress', label: '按完成进度' }
    ]

    const priorityOptions = [
      { value: 'high', label: '高优先级' },
      { value: 'medium', label: '中优先级' },
      { value: 'low', label: '低优先级' }
    ]

    this.setData({ sortOptions, priorityOptions })
  },

  // 加载数据
  async loadData() {
    // 检查登录状态
    const app = getApp()
    if (!app.globalData.userInfo || !app.globalData.openid) {
      console.log('用户未登录，尝试自动登录...')

      const LoginApi = require('../../utils/loginApi')
      const loginResult = await LoginApi.login()

      if (!loginResult.success) {
        wx.showToast({
          title: '请先登录',
          icon: 'none'
        })
        setTimeout(() => {
          wx.navigateTo({
            url: '/pages/login/index'
          })
        }, 1500)
        return
      }
    }

    this.loadTaskStats()
    this.loadTasks()
  },

  // 加载复习统计
  loadTaskStats() {
    const taskStats = [
      { label: '总计划', value: '24' },
      { label: '今日', value: '8' },
      { label: '已完成', value: '16' },
      { label: '完成率', value: '67%' }
    ]

    this.setData({ taskStats })
  },

  // 加载任务列表
  async loadTasks() {
    wx.showLoading({
      title: '加载中...',
      mask: true
    })

    try {
      // 使用SmartApi从云数据库获取任务
      const result = await SmartApi.getTasks({}, 50, 0)

      wx.hideLoading()

      if (result.success) {
        // 转换数据格式以适配页面显示
        const tasks = result.data.map(task => ({
          id: task._id || task.id,
          title: task.title,
          subject: task.subject,
          examName: task.examName || '',
          priority: task.priority || 'medium',
          priorityText: this.getPriorityText(task.priority),
          status: task.completed ? 'completed' : 'pending',
          statusText: task.completed ? '已完成' : '进行中',
          completed: task.completed || false,
          dueDate: this.formatDueDate(task.dueDate, task.dueTime),
          estimatedTime: task.estimatedDuration || '未设置',
          progress: task.progress || 0,
          subtasks: task.subtasks || [],
          completedSubtasks: task.subtasks ? task.subtasks.filter(sub => sub.completed).length : 0,
          showSubtasks: false,
          completionTime: task.completedTime ? this.formatTime(task.completedTime) : '',
          description: task.description || ''
        }))

        this.setData({ tasks })
        this.filterTasks()
        this.updateFilterCounts()
      } else {
        console.error('加载任务失败:', result.error)
        wx.showToast({
          title: '加载任务失败',
          icon: 'none'
        })
        this.setData({ tasks: [] })
      }
    } catch (error) {
      wx.hideLoading()
      console.error('加载任务异常:', error)
      wx.showToast({
        title: '加载任务失败',
        icon: 'none'
      })
      this.setData({ tasks: [] })
    }
  },

  // 获取优先级文本
  getPriorityText(priority) {
    const priorityMap = {
      'high': '高优先级',
      'medium': '中优先级',
      'low': '低优先级'
    }
    return priorityMap[priority] || '中优先级'
  },

  // 格式化截止日期
  formatDueDate(dueDate, dueTime) {
    if (!dueDate) return '未设置'

    const date = new Date(dueDate)
    const today = new Date()
    const tomorrow = new Date(today)
    tomorrow.setDate(tomorrow.getDate() + 1)

    let dateStr = ''
    if (date.toDateString() === today.toDateString()) {
      dateStr = '今天'
    } else if (date.toDateString() === tomorrow.toDateString()) {
      dateStr = '明天'
    } else {
      dateStr = `${date.getMonth() + 1}/${date.getDate()}`
    }

    if (dueTime) {
      dateStr += ` ${dueTime}`
    }

    return dateStr
  },

  // 格式化时间
  formatTime(timeStr) {
    if (!timeStr) return ''

    const date = new Date(timeStr)
    const now = new Date()
    const diffMs = now - date
    const diffMins = Math.floor(diffMs / (1000 * 60))
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

    if (diffMins < 1) return '刚刚'
    if (diffMins < 60) return `${diffMins}分钟前`
    if (diffHours < 24) return `${diffHours}小时前`
    if (diffDays < 7) return `${diffDays}天前`

    return `${date.getMonth() + 1}/${date.getDate()}`
  },

  // 筛选任务
  filterTasks() {
    const { tasks, currentFilter } = this.data
    let filteredTasks = tasks

    if (currentFilter !== 'all') {
      switch (currentFilter) {
        case 'today':
          filteredTasks = tasks.filter(task => task.dueDate && task.dueDate.includes('今天'))
          break
        case 'pending':
          filteredTasks = tasks.filter(task => !task.completed)
          break
        case 'completed':
          filteredTasks = tasks.filter(task => task.completed)
          break
        case 'overdue':
          // 这里可以添加逾期逻辑
          filteredTasks = []
          break
      }
    }

    this.setData({ filteredTasks })
    this.groupTasks()
  },

  // 按日期分组任务
  groupTasks() {
    const { filteredTasks } = this.data
    const groups = {}

    filteredTasks.forEach(task => {
      let dateKey = '其他'

      if (task.dueDate) {
        if (task.dueDate.includes('今天')) {
          dateKey = '今天'
        } else if (task.dueDate.includes('明天')) {
          dateKey = '明天'
        } else {
          dateKey = task.dueDate.split(' ')[0] || '其他'
        }
      } else if (task.completed && task.completionTime) {
        if (task.completionTime.includes('今天')) {
          dateKey = '今天'
        }
      }

      if (!groups[dateKey]) {
        groups[dateKey] = []
      }
      groups[dateKey].push(task)
    })

    const groupedTasks = Object.keys(groups).map(date => ({
      date,
      tasks: groups[date]
    }))

    this.setData({ groupedTasks })
  },

  // 更新筛选计数
  updateFilterCounts() {
    const { tasks, filterOptions } = this.data
    const updatedOptions = filterOptions.map(option => {
      let count = 0
      switch (option.value) {
        case 'all':
          count = tasks.length
          break
        case 'today':
          count = tasks.filter(task => task.dueDate && task.dueDate.includes('今天')).length
          break
        case 'pending':
          count = tasks.filter(task => !task.completed).length
          break
        case 'completed':
          count = tasks.filter(task => task.completed).length
          break
        case 'overdue':
          count = 0 // 逾期任务计数
          break
      }
      return { ...option, count }
    })

    this.setData({ filterOptions: updatedOptions })
  },

  // 切换筛选
  switchFilter(e) {
    const filter = e.currentTarget.dataset.filter
    this.setData({ currentFilter: filter })
    this.filterTasks()
  },

  // 切换任务状态
  async toggleTask(e) {
    const taskId = e.currentTarget.dataset.id
    const task = this.data.tasks.find(t => t.id === taskId)

    if (!task) return

    const newCompleted = !task.completed

    try {
      // 使用SmartApi更新任务状态
      const result = await SmartApi.completeTask(taskId, newCompleted)

      if (result.success) {
        // 更新本地数据
        const tasks = this.data.tasks.map(t => {
          if (t.id === taskId) {
            t.completed = newCompleted
            t.statusText = newCompleted ? '已完成' : '进行中'
            if (newCompleted) {
              t.completionTime = '刚刚'
            }
          }
          return t
        })

        this.setData({ tasks })
        this.filterTasks()
        this.updateFilterCounts()

        wx.showToast({
          title: newCompleted ? '复习完成！' : '重新开始复习',
          icon: 'success'
        })
      } else {
        wx.showToast({
          title: result.error || '操作失败',
          icon: 'none'
        })
      }
    } catch (error) {
      console.error('更新任务状态失败:', error)
      wx.showToast({
        title: '操作失败，请重试',
        icon: 'none'
      })
    }
  },

  // 切换子任务显示
  toggleSubtasks(e) {
    const taskId = e.currentTarget.dataset.id
    const tasks = this.data.tasks.map(task => {
      if (task.id === taskId) {
        task.showSubtasks = !task.showSubtasks
      }
      return task
    })

    this.setData({ tasks })
    this.filterTasks()
  },

  // 切换子任务状态
  toggleSubtask(e) {
    const { taskId, subtaskId } = e.currentTarget.dataset
    const tasks = this.data.tasks.map(task => {
      if (task.id === taskId) {
        task.subtasks = task.subtasks.map(subtask => {
          if (subtask.id === subtaskId) {
            subtask.completed = !subtask.completed
          }
          return subtask
        })
        task.completedSubtasks = task.subtasks.filter(s => s.completed).length
        task.progress = Math.round((task.completedSubtasks / task.subtasks.length) * 100)
      }
      return task
    })

    this.setData({ tasks })
    this.filterTasks()
  },

  // 显示任务操作菜单
  showTaskActions(e) {
    const task = e.currentTarget.dataset.task
    this.setData({
      selectedTask: task,
      showActionSheet: true
    })
  },

  // 隐藏操作菜单
  hideActionSheet() {
    this.setData({
      showActionSheet: false,
      selectedTask: null
    })
  },

  // 显示筛选菜单
  showFilterMenu() {
    this.setData({ showFilterMenu: true })
  },

  // 隐藏筛选菜单
  hideFilterMenu() {
    this.setData({ showFilterMenu: false })
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 阻止事件冒泡
  },

  // 执行任务操作
  executeTaskAction(e) {
    const action = e.currentTarget.dataset.action
    const task = this.data.selectedTask

    this.hideActionSheet()

    switch (action) {
      case 'viewDetail':
        this.viewTaskDetail({ currentTarget: { dataset: { id: task.id } } })
        break
      case 'edit':
        this.editTask({ currentTarget: { dataset: { id: task.id } } })
        break
      case 'start':
        this.startTask({ currentTarget: { dataset: { id: task.id } } })
        break
      case 'complete':
        this.toggleTask({ currentTarget: { dataset: { id: task.id } } })
        break
      case 'delete':
        this.deleteTask(task)
        break
    }
  },

  // 页面跳转和操作方法
  openSearch() {
    wx.navigateTo({
      url: '/pages/search/index?type=task'
    })
  },

  viewTaskDetail(e) {
    const taskId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: '/pages/task-detail/index?id=' + taskId
    })
  },

  addTask() {
    wx.navigateTo({
      url: '/pages/add-task/index'
    })
  },

  editTask(e) {
    const taskId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: '/pages/edit-task/index?id=' + taskId
    })
  },

  startTask(e) {
    const taskId = e.currentTarget.dataset.id
    wx.switchTab({
      url: '/pages/pomodoro/index'
    })
  },

  startPomodoro(e) {
    const taskId = e.currentTarget.dataset.id
    wx.switchTab({
      url: '/pages/pomodoro/index'
    })
  },

  deleteTask(task) {
    wx.showModal({
      title: '确认删除',
      content: `确定要删除任务"${task.title}"吗？此操作不可恢复。`,
      confirmText: '删除',
      confirmColor: '#FF4D4F',
      success: (res) => {
        if (res.confirm) {
          const tasks = this.data.tasks.filter(t => t.id !== task.id)
          this.setData({ tasks })
          this.filterTasks()
          this.updateFilterCounts()

          wx.showToast({
            title: '删除成功',
            icon: 'success'
          })
        }
      }
    })
  },

  // 获取空状态标题和消息
  getEmptyTitle() {
    const { currentFilter } = this.data
    const titles = {
      all: '还没有复习计划',
      today: '今天没有复习计划',
      pending: '没有待完成的复习',
      completed: '还没有完成的复习',
      overdue: '没有逾期的复习'
    }
    return titles[currentFilter] || '暂无数据'
  },

  getEmptyMessage() {
    const { currentFilter } = this.data
    const messages = {
      all: '制定你的第一个复习计划',
      today: '今天的复习都完成了',
      pending: '所有复习都已完成',
      completed: '完成一些复习来查看记录',
      overdue: '保持良好的备考习惯'
    }
    return messages[currentFilter] || '暂无相关数据'
  }
})