// 头像上传云函数
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const { action, data } = event

  try {
    switch (action) {
      case 'uploadAvatar':
        return await uploadAvatar(wxContext.OPENID, data)
      case 'deleteAvatar':
        return await deleteAvatar(wxContext.OPENID, data)
      case 'getUploadUrl':
        return await getUploadUrl(wxContext.OPENID, data)
      default:
        return { success: false, error: '未知操作' }
    }
  } catch (error) {
    console.error('头像上传云函数错误:', error)
    return { success: false, error: error.message }
  }
}

// 获取用户ID
async function getUserId(openid) {
  try {
    const userResult = await db.collection('users')
      .where({ openid: openid })
      .get()
    
    if (userResult.data.length > 0) {
      return userResult.data[0]._id
    }
    return null
  } catch (error) {
    console.error('获取用户ID失败:', error)
    return null
  }
}

// 上传头像
async function uploadAvatar(openid, { fileID, fileName, fileSize }) {
  try {
    console.log('开始处理头像上传:', { openid, fileID, fileName, fileSize })
    
    // 获取用户ID
    const userId = await getUserId(openid)
    if (!userId) {
      return { success: false, error: '用户不存在' }
    }

    // 获取用户当前信息
    const userResult = await db.collection('users')
      .where({ openid: openid })
      .get()
    
    if (userResult.data.length === 0) {
      return { success: false, error: '用户不存在' }
    }

    const user = userResult.data[0]
    const oldAvatarUrl = user.avatarUrl

    // 更新用户头像URL
    const updateResult = await db.collection('users')
      .where({ openid: openid })
      .update({
        data: {
          avatarUrl: fileID,
          updateTime: new Date()
        }
      })

    // 如果有旧头像且不是默认头像，删除旧头像
    if (oldAvatarUrl && oldAvatarUrl.startsWith('cloud://') && oldAvatarUrl !== fileID) {
      try {
        await cloud.deleteFile({
          fileList: [oldAvatarUrl]
        })
        console.log('删除旧头像成功:', oldAvatarUrl)
      } catch (deleteError) {
        console.error('删除旧头像失败:', deleteError)
        // 不影响主流程，继续执行
      }
    }

    console.log('头像上传成功:', fileID)
    
    return {
      success: true,
      data: {
        avatarUrl: fileID,
        fileName: fileName,
        fileSize: fileSize,
        uploadTime: new Date().toISOString()
      }
    }
  } catch (error) {
    console.error('上传头像失败:', error)
    return { success: false, error: error.message }
  }
}

// 删除头像
async function deleteAvatar(openid, { fileID }) {
  try {
    console.log('开始删除头像:', { openid, fileID })
    
    // 获取用户ID
    const userId = await getUserId(openid)
    if (!userId) {
      return { success: false, error: '用户不存在' }
    }

    // 删除云存储文件
    const deleteResult = await cloud.deleteFile({
      fileList: [fileID]
    })

    // 更新用户头像URL为空
    await db.collection('users')
      .where({ openid: openid })
      .update({
        data: {
          avatarUrl: '',
          updateTime: new Date()
        }
      })

    console.log('删除头像成功:', fileID)
    
    return {
      success: true,
      data: {
        deletedFileID: fileID,
        deleteResult: deleteResult
      }
    }
  } catch (error) {
    console.error('删除头像失败:', error)
    return { success: false, error: error.message }
  }
}

// 获取上传URL（用于直接上传到云存储）
async function getUploadUrl(openid, { fileName, fileType }) {
  try {
    console.log('获取上传URL:', { openid, fileName, fileType })
    
    // 获取用户ID
    const userId = await getUserId(openid)
    if (!userId) {
      return { success: false, error: '用户不存在' }
    }

    // 生成唯一的文件名
    const timestamp = Date.now()
    const randomStr = Math.random().toString(36).substr(2, 9)
    const fileExtension = fileName.split('.').pop()
    const cloudPath = `avatars/${userId}/${timestamp}_${randomStr}.${fileExtension}`

    return {
      success: true,
      data: {
        cloudPath: cloudPath,
        uploadUrl: `cloud://${cloud.DYNAMIC_CURRENT_ENV}.${cloudPath}`
      }
    }
  } catch (error) {
    console.error('获取上传URL失败:', error)
    return { success: false, error: error.message }
  }
}
