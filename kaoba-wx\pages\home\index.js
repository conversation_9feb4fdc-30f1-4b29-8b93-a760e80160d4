// pages/home/<USER>
const SmartApi = require('../../utils/smartApi')

Page({
  data: {
    greetingTime: '',
    greetingMessage: '',
    currentDate: '',
    lunarDate: '',
    weather: null,
    nearestExam: null,
    countdown: [],
    todayTasks: [],
    completedTasks: 0,
    totalTasks: 0,
    todayStats: [],
    quickActions: [],
    recentActivities: [],
    fabExpanded: false,
    fabMenuItems: [],
    loading: false
  },

  onLoad() {
    // 检查登录状态
    this.checkLoginStatus()
    this.initPage()
  },

  // 检查登录状态
  checkLoginStatus() {
    const app = getApp()
    const loginStatus = app.checkLoginStatus()

    if (!loginStatus.isLoggedIn) {
      // 未登录，跳转到登录页
      wx.redirectTo({
        url: '/pages/login/index'
      })
      return false
    }

    return true
  },

  async onShow() {
    // 检查登录状态
    const app = getApp()
    if (!app.globalData.userInfo || !app.globalData.openid) {
      console.log('用户未登录，尝试自动登录...')

      const LoginApi = require('../../utils/loginApi')
      const loginResult = await LoginApi.login()

      if (!loginResult.success) {
        // 登录失败，显示登录提示
        this.setData({
          greeting: '请先登录',
          todayTasks: [],
          recentActivities: []
        })
        return
      }
    }

    // 检查数据是否已初始化
    if (app.globalData.dbInitialized) {
      this.refreshData()
    } else {
      // 如果数据未初始化，延迟加载数据
      setTimeout(() => {
        this.refreshData()
      }, 2000)
    }

    // 更新tabBar选中状态
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 0
      })
    }
  },

  onPullDownRefresh() {
    this.refreshData()
    setTimeout(() => {
      wx.stopPullDownRefresh()
    }, 1000)
  },

  // 初始化页面
  initPage() {
    this.setGreeting()
    this.setDateInfo()
    this.initQuickActions()
    this.initFabMenu()
    this.loadData()
  },

  // 设置问候语
  setGreeting() {
    const hour = new Date().getHours()
    const app = getApp()
    const userInfo = app.globalData.userInfo
    let greetingTime, greetingMessage

    // 获取用户昵称
    const userName = userInfo?.nickName || '同学'

    if (hour < 6) {
      greetingTime = '深夜好'
      greetingMessage = `${userName}，夜深了，注意休息哦`
    } else if (hour < 12) {
      greetingTime = '早上好'
      greetingMessage = `${userName}，新的一天，加油学习！`
    } else if (hour < 18) {
      greetingTime = '下午好'
      greetingMessage = `${userName}，继续保持学习状态`
    } else {
      greetingTime = '晚上好'
      greetingMessage = `${userName}，今天学习得怎么样？`
    }

    this.setData({
      greetingTime,
      greetingMessage,
      userInfo: userInfo
    })
  },

  // 设置日期信息
  setDateInfo() {
    const now = new Date()
    const currentDate = now.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      weekday: 'long'
    })

    this.setData({
      currentDate,
      lunarDate: '农历十一月初八' // 这里可以接入农历API
    })
  },

  // 初始化快捷操作
  initQuickActions() {
    const quickActions = [
      {
        id: 'add_task',
        label: '添加任务',
        icon: '📝',
        bgColor: '#E6F7FF',
        action: 'addTask'
      },
      {
        id: 'add_exam',
        label: '添加考试',
        icon: '📅',
        bgColor: '#F6FFED',
        action: 'addExam'
      },
      {
        id: 'start_pomodoro',
        label: '开始专注',
        icon: '🍅',
        bgColor: '#FFF2E8',
        action: 'startPomodoro'
      },
      {
        id: 'view_stats',
        label: '查看统计',
        icon: '📊',
        bgColor: '#F9F0FF',
        action: 'viewStats'
      }
    ]

    this.setData({ quickActions })
  },

  // 初始化悬浮按钮菜单
  initFabMenu() {
    const fabMenuItems = [
      {
        id: 'add_task',
        label: '添加任务',
        icon: '📝',
        color: '#52C41A',
        action: 'addTask'
      },
      {
        id: 'add_exam',
        label: '添加考试',
        icon: '📅',
        color: '#1890FF',
        action: 'addExam'
      },
      {
        id: 'start_pomodoro',
        label: '开始专注',
        icon: '🍅',
        color: '#FA8C16',
        action: 'startPomodoro'
      }
    ]

    this.setData({ fabMenuItems })
  },

  // 加载数据
  async loadData() {
    this.setData({ loading: true })
    try {
      await Promise.all([
        this.loadNearestExam(),
        this.loadTodayTasks(),
        this.loadTodayStats(),
        this.loadRecentActivities()
      ])
    } catch (error) {
      console.error('加载数据失败:', error)
      wx.showToast({
        title: '数据加载失败',
        icon: 'error'
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  // 刷新数据
  refreshData() {
    this.setGreeting()
    this.setDateInfo()
    this.loadData()
  },

  // 加载最近考试
  async loadNearestExam() {
    try {
      const result = await SmartApi.getUpcomingExams(30, 1) // 获取30天内最近的1个考试

      if (result.success && result.data.length > 0) {
        const exam = result.data[0]
        const nearestExam = {
          id: exam._id,
          name: exam.title,
          date: exam.examDate,
          subject: exam.subject,
          location: exam.location,
          status: '备考中',
          preparationProgress: 65, // 这里可以根据相关任务完成情况计算
          daysLeft: Math.ceil((new Date(exam.examDate) - new Date()) / (1000 * 60 * 60 * 24))
        }

        // 计算倒计时
        const countdown = this.calculateCountdown(nearestExam.date)

        this.setData({
          nearestExam,
          countdown
        })
      } else {
        // 没有即将到来的考试
        this.setData({
          nearestExam: null,
          countdown: []
        })
      }
    } catch (error) {
      console.error('加载最近考试失败:', error)
      // 设置空状态
      this.setData({
        nearestExam: null,
        countdown: []
      })
    }
  },

  // 计算倒计时
  calculateCountdown(targetDate) {
    const now = new Date()
    const target = new Date(targetDate)
    const diff = target - now

    if (diff <= 0) {
      return []
    }

    const days = Math.floor(diff / (1000 * 60 * 60 * 24))
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))

    return [
      { value: days, unit: '天' },
      { value: hours, unit: '时' },
      { value: minutes, unit: '分' }
    ]
  },

  // 加载今日任务
  async loadTodayTasks() {
    try {
      const today = new Date()
      today.setHours(0, 0, 0, 0)
      const tomorrow = new Date(today)
      tomorrow.setDate(tomorrow.getDate() + 1)

      const result = await SmartApi.getTasks({
        dateRange: {
          start: today.toISOString(),
          end: tomorrow.toISOString()
        }
      }, 10) // 限制显示10个任务

      if (result.success) {
        const todayTasks = result.data.map(task => ({
          id: task._id,
          title: task.title,
          subject: task.subject,
          priority: task.priority,
          priorityText: this.getPriorityText(task.priority),
          estimatedTime: task.estimatedTime ? `${task.estimatedTime}分钟` : '未设置',
          completed: task.completed,
          statusText: task.completed ? '已完成' : '进行中'
        }))

        const completedTasks = todayTasks.filter(task => task.completed).length
        const totalTasks = todayTasks.length

        this.setData({
          todayTasks,
          completedTasks,
          totalTasks
        })
      }
    } catch (error) {
      console.error('加载今日任务失败:', error)
      // 设置空状态
      this.setData({
        todayTasks: [],
        completedTasks: 0,
        totalTasks: 0
      })
    }
  },

  // 获取优先级文本
  getPriorityText(priority) {
    const priorityMap = {
      'high': '高优先级',
      'medium': '中优先级',
      'low': '低优先级'
    }
    return priorityMap[priority] || '普通'
  },

  // 加载今日统计
  async loadTodayStats() {
    try {
      const today = new Date()
      today.setHours(0, 0, 0, 0)
      const tomorrow = new Date(today)
      tomorrow.setDate(tomorrow.getDate() + 1)

      const dateRange = {
        start: today.toISOString(),
        end: tomorrow.toISOString()
      }

      // 并行获取各种统计数据
      const [taskStatsResult, studyStatsResult, pomodoroStatsResult] = await Promise.all([
        SmartApi.getTaskStats(dateRange),
        SmartApi.getStudyStats(dateRange),
        SmartApi.getPomodoroStats(dateRange)
      ])

      let studyTime = '0h'
      let completedTasks = '0个'
      let pomodoroCount = '0次'
      let efficiency = '0%'

      if (studyStatsResult.success) {
        const totalMinutes = studyStatsResult.data.totalDuration || 0
        const hours = Math.floor(totalMinutes / 60)
        const minutes = Math.floor(totalMinutes % 60)
        studyTime = hours > 0 ? `${hours}.${Math.floor(minutes/6)}h` : `${minutes}m`
      }

      if (taskStatsResult.success) {
        completedTasks = `${taskStatsResult.data.completed || 0}个`
        if (taskStatsResult.data.total > 0) {
          efficiency = `${Math.round((taskStatsResult.data.completed / taskStatsResult.data.total) * 100)}%`
        }
      }

      if (pomodoroStatsResult.success) {
        pomodoroCount = `${pomodoroStatsResult.data.completedSessions || 0}次`
      }

      const todayStats = [
        {
          label: '学习时长',
          value: studyTime,
          icon: '⏰',
          color: '#1890FF'
        },
        {
          label: '完成任务',
          value: completedTasks,
          icon: '✅',
          color: '#52C41A'
        },
        {
          label: '专注次数',
          value: pomodoroCount,
          icon: '🍅',
          color: '#FA8C16'
        },
        {
          label: '学习效率',
          value: efficiency,
          icon: '📈',
          color: '#722ED1'
        }
      ]

      this.setData({ todayStats })
    } catch (error) {
      console.error('加载今日统计失败:', error)
      // 使用默认数据
      const todayStats = [
        {
          label: '学习时长',
          value: '0h',
          icon: '⏰',
          color: '#1890FF'
        },
        {
          label: '完成任务',
          value: '0个',
          icon: '✅',
          color: '#52C41A'
        },
        {
          label: '专注次数',
          value: '0次',
          icon: '🍅',
          color: '#FA8C16'
        },
        {
          label: '学习效率',
          value: '0%',
          icon: '📈',
          color: '#722ED1'
        }
      ]

      this.setData({ todayStats })
    }
  },

  // 加载最近活动
  loadRecentActivities() {
    const recentActivities = [
      {
        id: 'activity_001',
        title: '完成数学练习题',
        time: '2小时前',
        icon: '📝',
        status: 'success',
        statusText: '已完成'
      },
      {
        id: 'activity_002',
        title: '25分钟专注学习',
        time: '3小时前',
        icon: '🍅',
        status: 'success',
        statusText: '已完成'
      },
      {
        id: 'activity_003',
        title: '英语单词测试',
        time: '昨天',
        icon: '📖',
        status: 'warning',
        statusText: '需复习'
      }
    ]

    this.setData({ recentActivities })
  },

  // 切换任务状态
  async toggleTask(e) {
    const taskId = e.currentTarget.dataset.id
    const task = this.data.todayTasks.find(t => t.id === taskId)

    if (!task) return

    const newCompleted = !task.completed

    try {
      // 调用API更新任务状态
      const result = await SmartApi.completeTask(taskId, newCompleted)

      if (result.success) {
        // 更新本地数据
        const tasks = this.data.todayTasks.map(task => {
          if (task.id === taskId) {
            task.completed = newCompleted
            task.statusText = newCompleted ? '已完成' : '进行中'
          }
          return task
        })

        const completedTasks = tasks.filter(task => task.completed).length

        this.setData({
          todayTasks: tasks,
          completedTasks
        })

        // 显示反馈
        wx.showToast({
          title: newCompleted ? '任务完成！' : '任务重新开始',
          icon: 'success'
        })

        // 如果任务完成，刷新统计数据
        if (newCompleted) {
          this.loadTodayStats()
        }
      } else {
        wx.showToast({
          title: '操作失败',
          icon: 'error'
        })
      }
    } catch (error) {
      console.error('切换任务状态失败:', error)
      wx.showToast({
        title: '操作失败',
        icon: 'error'
      })
    }
  },

  // 切换悬浮按钮
  toggleFab() {
    this.setData({
      fabExpanded: !this.data.fabExpanded
    })
  },

  // 执行快捷操作
  executeQuickAction(e) {
    const action = e.currentTarget.dataset.action
    this.handleAction(action)
  },

  // 执行悬浮按钮操作
  executeFabAction(e) {
    const action = e.currentTarget.dataset.action
    this.setData({ fabExpanded: false })
    this.handleAction(action)
  },

  // 处理操作
  handleAction(action) {
    switch (action) {
      case 'addTask':
        this.addTask()
        break
      case 'addExam':
        this.addExam()
        break
      case 'startPomodoro':
        this.startPomodoro()
        break
      case 'viewStats':
        this.viewDataCenter()
        break
      default:
        console.log('未知操作:', action)
    }
  },

  // 页面跳转方法
  viewExamDetail() {
    wx.navigateTo({
      url: '/pages/exam-detail/index?id=' + this.data.nearestExam.id
    })
  },

  startStudy() {
    wx.switchTab({
      url: '/pages/task-center/index'
    })
  },

  viewAllTasks() {
    wx.switchTab({
      url: '/pages/task-center/index'
    })
  },

  viewTaskDetail(e) {
    const taskId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: '/pages/task-detail/index?id=' + taskId
    })
  },

  addTask() {
    wx.navigateTo({
      url: '/pages/add-task/index'
    })
  },

  addExam() {
    wx.navigateTo({
      url: '/pages/add-exam/index'
    })
  },

  startPomodoro() {
    wx.switchTab({
      url: '/pages/pomodoro/index'
    })
  },

  viewDataCenter() {
    wx.switchTab({
      url: '/pages/data-center/index'
    })
  }
})
